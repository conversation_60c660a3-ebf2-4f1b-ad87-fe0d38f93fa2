//
//  CreativeCenterViewController.swift
//  Shuxiaoqi
//
//  Created by yongsheng ye on 2025/3/24.
//
//  创作中心

import UIKit
import SnapKit
import Kingfisher
import MJRefresh


struct StatsResponse: Codable {
    let publishCount: Int
    let playCount: Int
    let likeCount: Int
    let fansCount: Int
}

class CreativeCenterViewController: BaseViewController {
    
    // MARK: - 数据模型
    struct UserInfo {
        let avatar: String
        let username: String
        let tags: [String]
        let stats: UserStats
    }
    
    struct UserStats {
        let publishCount: Int
        let playCount: Int
        let likeCount: Int
        let fansCount: Int
    }
    
    struct VideoItem {
        let id: String
        let title: String
        let duration: String          // 已格式化 "mm:ss"
        let timeAgo: String
        let thumbnail: String         // 封面 URL
        let size: Int                 // Byte
        let worksType: Int            // 1=视频 2=笔记
        let worksUrl: String          // 线上地址或本地路径

        // ===== 用于"编辑模式"预填充 =====
        let categoryId: Int?          // 分类 id
        let categoryName: String?
        let privacy: Int?             // 1-公开 2-关注我的 3-仅自己
        let allowComment: Int?
        let followComment: Int?
        let lat: String?
        let lng: String?
        let address: String?
        let extValue: String?         // 定时发布时间 yyyy-MM-dd HH:mm

        // 可选：原始时长（秒）
        let durationSeconds: Int?

        init(id: String,
             title: String,
             duration: String,
             timeAgo: String,
             thumbnail: String,
             size: Int,
             worksType: Int,
             worksUrl: String,
             categoryId: Int? = nil,
             categoryName: String? = nil,
             privacy: Int? = nil,
             allowComment: Int? = nil,
             followComment: Int? = nil,
             lat: String? = nil,
             lng: String? = nil,
             address: String? = nil,
             extValue: String? = nil,
             durationSeconds: Int? = nil) {
            self.id = id
            self.title = title
            self.duration = duration
            self.timeAgo = timeAgo
            self.thumbnail = thumbnail
            self.size = size
            self.worksType = worksType
            self.worksUrl = worksUrl
            self.categoryId = categoryId
            self.categoryName = categoryName
            self.privacy = privacy
            self.allowComment = allowComment
            self.followComment = followComment
            self.lat = lat
            self.lng = lng
            self.address = address
            self.extValue = extValue
            self.durationSeconds = durationSeconds
        }
    }
    
    struct DraftVideoItem {
        let id: String
        let title: String
        let duration: String
        let timeAgo: String
        let thumbnail: String
        let size: Int
        let worksType: Int
        let worksUrl: String
        
        static func from(_ data: PersonalWorksListDetailData) -> DraftVideoItem {
            return DraftVideoItem(
                id: String(data.id),
                title: data.worksTitle,
                duration: formatDuration(seconds: data.duration),
                timeAgo: formatTimeAgo(timeString: data.createTime),
                thumbnail: data.worksCoverImg,
                size: data.size,
                worksType: data.worksType,
                worksUrl: data.worksUrl
            )
        }
        
        private static func formatDuration(seconds: Int) -> String {
            let minutes = seconds / 60
            let remainingSeconds = seconds % 60
            return String(format: "%02d:%02d", minutes, remainingSeconds)
        }
        
        private static func formatTimeAgo(timeString: String?) -> String {
            guard let timeString = timeString else { return "未知时间" }
            
            let dateFormatter = DateFormatter()
            dateFormatter.dateFormat = "yyyy-MM-dd HH:mm:ss"
            
            if let date = dateFormatter.date(from: timeString) {
                let now = Date()
                let calendar = Calendar.current
                
                let components = calendar.dateComponents([.year, .month, .day, .hour, .minute], from: date, to: now)
                
                if let year = components.year, year > 0 {
                    return "\(year)年前"
                }
                if let month = components.month, month > 0 {
                    return "\(month)个月前"
                }
                if let day = components.day, day > 0 {
                    if day == 1 { return "昨天" }
                    return "\(day)天前"
                }
                if let hour = components.hour, hour > 0 {
                    return "\(hour)小时前"
                }
                if let minute = components.minute, minute > 0 {
                    return "\(minute)分钟前"
                }
                return "刚刚"
            }
            return timeString
        }
    }
    
    // MARK: - UI 组件
    
    // 背景头像图片视图
    private lazy var backgroundImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFill
        imageView.clipsToBounds = true
        imageView.image = UIImage(named: "default_avatar") // 默认头像，可从用户数据中更新
        return imageView
    }()
    
    // 新增：背景图片的原始高度
    private let backgroundImageHeight: CGFloat = UIScreen.main.bounds.width
    
    // 新增：背景图片的原始宽度
    private var backgroundImageOriginalWidth: CGFloat = 0
    
    // 新增：背景图片容器视图
    private lazy var backgroundImageContainer: UIView = {
        let view = UIView()
        view.clipsToBounds = true
        return view
    }()
    
    // 新增：tableHeaderView的原始高度
    private var headerViewOriginalHeight: CGFloat = 0
    
    // 新增：tableHeaderView的原始Y坐标
    private var headerViewOriginalY: CGFloat = 0
    
    // 顶部渐变蒙版
    private lazy var overlayView: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        
        // 创建渐变层
        let gradientLayer = CAGradientLayer()
        gradientLayer.colors = [
            UIColor.white.withAlphaComponent(0.2).cgColor, // 上方为0.2透明度的白色
            UIColor(hex: "#F5F5F5").cgColor                // 下方为F5F5F5色
        ]
        gradientLayer.locations = [0.0, 1.0]
        gradientLayer.startPoint = CGPoint(x: 0.5, y: 0.0)
        gradientLayer.endPoint = CGPoint(x: 0.5, y: 1.0)
        
        // 禁用隐式动画
        CATransaction.begin()
        CATransaction.setDisableActions(true)
        view.layer.addSublayer(gradientLayer)
        CATransaction.commit()
        
        return view
    }()
    
    // 自定义导航栏
    private lazy var customNavBar: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        return view
    }()
    
    // 导航栏标题
    private lazy var navTitleLabel: UILabel = {
        let label = UILabel()
        label.text = "创作中心"
        label.textColor = UIColor(hex: "#333333")
        label.font = .boldSystemFont(ofSize: 16)
        label.textAlignment = .center
        return label
    }()
    
    // 返回按钮
    private lazy var customBackBtn: UIButton = {
        let button = UIButton(type: .custom)
        button.setImage(UIImage(named: "nav_back"), for: .normal)
        button.addTarget(self, action: #selector(customBackButtonTapped), for: .touchUpInside)
        return button
    }()
    
    // 内容卡片
    private lazy var contentCardView: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        view.layer.cornerRadius = 12
        view.layer.shadowColor = UIColor.black.withAlphaComponent(0.1).cgColor
        view.layer.shadowOffset = CGSize(width: 0, height: 2)
        view.layer.shadowRadius = 10
        view.layer.shadowOpacity = 1
        return view
    }()
    
    // 头像视图
    private lazy var avatarImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFill
        imageView.clipsToBounds = true
        imageView.layer.cornerRadius = 40 // 80*80的圆形头像
        imageView.image = UIImage(named: "default_avatar")
        return imageView
    }()
    
    // 渐变色头像框
    private lazy var avatarGradientFrameView: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        view.layer.cornerRadius = 42 // 头像半径+边框宽度
        view.layer.masksToBounds = true
        
        // 创建渐变层
        let gradientLayer = CAGradientLayer()
        gradientLayer.colors = [
            UIColor(hex: "#FF8D36").cgColor,
            UIColor(hex: "#FF6236").cgColor,
            UIColor(hex: "#FF3B74").cgColor
        ]
        gradientLayer.startPoint = CGPoint(x: 0, y: 0)
        gradientLayer.endPoint = CGPoint(x: 1, y: 1)
        gradientLayer.locations = [0, 0.5, 1]
        view.layer.addSublayer(gradientLayer)
        
        return view
    }()
    
    // 用户名标签
    private lazy var usernameLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 18, weight: .bold)
        label.textColor = UIColor(hex: "#333333")
        label.text = "用户名称用户名称1"
        return label
    }()
    
    // 统计信息容器
    private lazy var statsContainer: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        return view
    }()
    
    // 粉丝数统计
    private lazy var fansCountView: CreativeCenterStatsView = {
        let view = CreativeCenterStatsView()
        view.configure(count: "1,234", title: "发布数")
        return view
    }()
    
    // 总获赞统计
    private lazy var likesCountView: CreativeCenterStatsView = {
        let view = CreativeCenterStatsView()
        view.configure(count: "2.8万", title: "总播放量")
        return view
    }()
    
    // 获赞数统计
    private lazy var videoCountView: CreativeCenterStatsView = {
        let view = CreativeCenterStatsView()
        view.configure(count: "123.4万", title: "获赞数")
        return view
    }()
    
    // 粉丝数统计
    private lazy var followCountView: CreativeCenterStatsView = {
        let view = CreativeCenterStatsView()
        view.configure(count: "123.4万", title: "粉丝数")
        return view
    }()
    
    // 底部功能按钮区域
    private lazy var buttonsContainer: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        return view
    }()
    
    // 发布作品按钮
    private lazy var publishButton: UIButton = {
        // 创建渐变色按钮 #FFC300-#FF8D36
        let button = createGradientButton(
            title: "发布作品",
            iconName: "icon_publish",
            startColor: UIColor(hex: "#FFC300"),
            endColor: UIColor(hex: "#FF8D36")
        )
        button.addTarget(self, action: #selector(publishButtonTapped), for: .touchUpInside)
        return button
    }()
    
    // 内容管理按钮
    private lazy var contentManageButton: UIButton = {
        // 创建渐变色按钮 #FF8D36-#FF5858
        let button = createGradientButton(
            title: "内容管理",
            iconName: "icon_content_manage",
            startColor: UIColor(hex: "#FF8D36"),
            endColor: UIColor(hex: "#FF5858")
        )
        button.addTarget(self, action: #selector(contentManageButtonTapped), for: .touchUpInside)
        return button
    }()
    
    // 规则中心按钮
    private lazy var ruleCenterButton: UIButton = {
        // 创建渐变色按钮 #FF5858-#DC2A68
        let button = createGradientButton(
            title: "规则中心",
            iconName: "icon_rule_center",
            startColor: UIColor(hex: "#FF5858"),
            endColor: UIColor(hex: "#DC2A68")
        )
        button.addTarget(self, action: #selector(ruleCenterButtonTapped), for: .touchUpInside)
        return button
    }()
    
    // 新增 UITableView 相关组件
    private lazy var tableView: UITableView = {
        let tableView = UITableView(frame: .zero, style: .plain)
        tableView.backgroundColor = .clear
        tableView.separatorStyle = .none
        tableView.delegate = self
        tableView.dataSource = self
        tableView.register(CreativeVideoItemCell.self, forCellReuseIdentifier: "CreativeVideoItemCell")
        // 设置 contentInset 以避免内容被导航栏遮挡，后续会根据滚动调整
        tableView.contentInsetAdjustmentBehavior = .never // 我们自己处理 safeArea
        return tableView
    }()

    // Table Header View
    private lazy var tableHeaderView: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        // HeaderView 的高度需要动态计算或固定设置
        return view
    }()

    // 自定义分段控件 - 已发布
    private lazy var publishedSegmentButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("已发布 99", for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 15, weight: .bold) // 默认选中
        button.setTitleColor(UIColor(hex: "#FF5936"), for: .normal) // 默认选中
        button.addTarget(self, action: #selector(segmentButtonTapped(_:)), for: .touchUpInside)
        button.tag = 0 // 用于区分按钮
        return button
    }()

    // 自定义分段控件 - 草稿箱
    private lazy var draftSegmentButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("草稿箱 99", for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 15, weight: .bold) // 默认选中
        button.setTitleColor(UIColor(hex: "#333333"), for: .normal)
        button.addTarget(self, action: #selector(segmentButtonTapped(_:)), for: .touchUpInside)
        button.tag = 1 // 用于区分按钮
        return button
    }()

    // 分段指示器视图
    private lazy var segmentIndicatorView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor(hex: "#FF5936")
        return view
    }()

    // 修改：重命名为悬浮的分段控件容器
    private lazy var floatingSegmentControl: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor(hex: "#F5F5F5") // 背景色
        // 添加按钮和指示器 (在 setupUI 中完成)
        return view
    }()

    // 新增：分段控件在 Header 中的占位符
    private lazy var segmentPlaceholderView: UIView = {
        let view = UIView()
        view.backgroundColor = .clear // 占位符通常是透明的
        return view
    }()

    // 新增：表脚视图
    private var tableFooterContainerView: CreativeCenterFooterView {
        // 动态获取底部安全区
        let safeArea = view.window?.safeAreaInsets.bottom ?? UIApplication.shared.windows.first?.safeAreaInsets.bottom ?? 0
        let footerHeight: CGFloat = 35 + 12 + 12 + safeArea
        let footer = CreativeCenterFooterView(frame: CGRect(x: 0, y: 0, width: tableView.bounds.width, height: footerHeight), bottomSafeArea: safeArea)
        footer.viewAllButton.addTarget(self, action: #selector(viewAllButtonTapped), for: .touchUpInside)
        return footer
    }

    // 新增：标签容器 StackView
    private lazy var tagsStackView: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .horizontal
        stackView.spacing = 8 // 标签之间的间距
        stackView.alignment = .center
        stackView.distribution = .fill // Or .equalSpacing if tags have varying content but should space out
        return stackView
    }()

    // MARK: - 数据源
    private var publishedItems: [VideoItem] = [] // 已发布数据
    private var draftItems: [DraftVideoItem] = []     // 草稿箱数据
    // 保存原始草稿数据，用于编辑时获取完整信息
    private var originalDraftData: [PersonalWorksListDetailData] = []
    private var currentSegmentIndex: Int = 0 // 0: 已发布, 1: 草稿箱
    
    // 每个列表在创作中心页面最多展示的 Cell 数量
    private let maxDisplayCount: Int = 10
    
    // 分页相关属性
    private var currentPage: Int = 0
    private var pageSize: Int = 10
    private var hasMoreData: Bool = true
    private var isPublishedLoading: Bool = false  // 已发布列表加载状态
    private var isDraftLoading: Bool = false      // 草稿箱列表加载状态

    // MARK: - 网络请求方法
    
    /// 获取用户信息
    private func fetchUserInfo() {
        // TODO: 实现网络请求
        // 示例请求：
        APIManager.shared.getUserInfo { [weak self] result in
             switch result {
             case .success(let response):
                 self?.handleUserInfoResponse(response)
             case .failure(let error):
                 self?.handleError(error)
             }
         }
    }
    
    /// 处理用户信息响应
    private func handleUserInfoResponse(_ response: UserInfoResponse) {
        guard let userData = response.data else {
            handleError(NSError(domain: "", code: -1, userInfo: [NSLocalizedDescriptionKey: response.displayMessage]))
            return
        }
        
        // 处理标签：优先使用 labels 数组，其次使用 personalitySign snapshot
        let tagsList: [String]
        if !userData.labels.isEmpty {
            tagsList = userData.labels
        } else {
            tagsList = []
        }
        
        // 创建 UserInfo 对象
        let userInfo = UserInfo(
            avatar: userData.wxAvator,
            username: userData.displayNickName,
            tags: tagsList,
            stats: UserStats(
                publishCount: userData.worksNumber,
                playCount: userData.watchNumber,
                likeCount: userData.likeNumber,
                fansCount: userData.fansNumber
            )
        )
        
        // 更新 UI
        updateUserInfoUI(with: userInfo)
    }
    
    /// 获取已发布列表
    private func fetchPublishedList(isRefresh: Bool = false) {
        if isPublishedLoading { return }
        isPublishedLoading = true
        
        // 如果是刷新，重置页码
        if isRefresh {
            currentPage = 0
            hasMoreData = true
        }
        
        print("已发布请求 - 页码: \(currentPage), 每页数量: \(pageSize), 是否刷新: \(isRefresh)")
        
        // 如果没有更多数据，直接返回
        if !hasMoreData && !isRefresh {
            isPublishedLoading = false
            return
        }
        
        // 调用获取个人作品列表API，添加 state = 2 的筛选参数
        APIManager.shared.getPersonalWorksList(page: currentPage, size: pageSize, state: 5) { [weak self] result in
            guard let self = self else { return }
            
            self.isPublishedLoading = false
            
            // 结束刷新状态
            if isRefresh {
                self.tableView.mj_header?.endRefreshing()
            } else {
                self.tableView.mj_footer?.endRefreshing()
            }
            
            switch result {
            case .success(let response):
                if let responseData = response.data {
                    self.handlePublishedListResponse(responseData: responseData, isRefresh: isRefresh)
                } else {
                    print("API响应中没有数据字段或数据为空")
                    if isRefresh {
                        self.publishedItems = []
                    }
                    self.updateSegmentTitles()
                    self.tableView.reloadData()
                }
                
            case .failure(let error):
                print("获取已发布列表失败: \(error.localizedDescription)")
                self.showToast("获取已发布列表失败，请稍后重试")
            }
        }
    }
    
    /// 获取草稿箱列表
    private func fetchDraftList(isRefresh: Bool = false) {
        if isDraftLoading {
            print("草稿箱请求被拦截 - 当前正在加载中")
            return
        }
        isDraftLoading = true
        
        // 如果是刷新，重置页码
        if isRefresh {
            currentPage = 0
            hasMoreData = true
        }
        
        print("草稿箱请求 - 页码: \(currentPage), 每页数量: \(pageSize), 是否刷新: \(isRefresh)")
        
        // 如果没有更多数据，直接返回
        if !hasMoreData && !isRefresh {
            print("草稿箱请求被拦截 - 没有更多数据且不是刷新操作")
            isDraftLoading = false
            return
        }
        
        // 调用获取草稿箱列表API
        APIManager.shared.getVideoWorksDraftsList(page: currentPage, size: pageSize) { [weak self] result in
            guard let self = self else { return }
            
            print("草稿箱请求完成 - 开始处理响应")
            self.isDraftLoading = false
            
            // 结束刷新状态
            if isRefresh {
                self.tableView.mj_header?.endRefreshing()
            } else {
                self.tableView.mj_footer?.endRefreshing()
            }
            
            switch result {
            case .success(let response):
                print("草稿箱响应 - 状态码:, 消息: \(response.displayMessage)")
                if let responseData = response.data {
                    print("草稿箱数据 - 总数: \(responseData.total), 当前页数据量: \(responseData.list.count)")
                    self.handleDraftListResponse(responseData: responseData, isRefresh: isRefresh)
                } else {
                    print("草稿箱响应 - 数据为空")
                    if isRefresh {
                        self.draftItems = []
                    }
                    self.updateSegmentTitles()
                    self.tableView.reloadData()
                }
                
            case .failure(let error):
                print("草稿箱请求失败: \(error.localizedDescription)")
                self.showToast("获取草稿箱列表失败，请稍后重试")
            }
        }
    }
    
    /// 处理已发布列表响应
    private func handlePublishedListResponse(responseData: PersonalWorksListData, isRefresh: Bool) {
        let newItems = responseData.list.map { item -> VideoItem in
            return VideoItem(
                id: String(item.id),
                title: item.worksTitle,
                duration: formatDuration(seconds: item.duration),
                timeAgo: formatTimeAgo(timeString: item.createTime),
                thumbnail: item.worksCoverImg,
                size: item.size,
                worksType: item.worksType,
                worksUrl: item.worksUrl,
                categoryId: (item.worksCategoryId == 0 ? nil : item.worksCategoryId),
                categoryName: item.worksCategoryName,
                privacy: item.privacy,
                allowComment: item.allowComment,
                followComment: item.followComment,
                lat: item.lat,
                lng: item.lng,
                address: item.address,
                extValue: item.extValue,
                durationSeconds: item.duration
            )
        }
        
        // 更新数据源
        if isRefresh {
            publishedItems = newItems
        } else {
            publishedItems.append(contentsOf: newItems)
        }
        
        // 更新分页状态
        hasMoreData = newItems.count >= pageSize
        if !hasMoreData {
            tableView.mj_footer?.endRefreshingWithNoMoreData()
        }
        
        // 更新分段控件标题
        updateSegmentTitles()
        
        // 刷新表格
        tableView.reloadData()
        
        // 更新占位视图
        updatePlaceholderView()

        if currentPage == 0 || publishedItems.count < pageSize {
            tableView.mj_footer?.isHidden = true
        } else {
            tableView.mj_footer?.isHidden = false
            if !hasMoreData {
                tableView.mj_footer?.endRefreshingWithNoMoreData()
            }
        }
    }
    
    /// 处理草稿箱列表响应
    private func handleDraftListResponse(responseData: PersonalWorksListData, isRefresh: Bool) {
        let newItems = responseData.list.map { DraftVideoItem.from($0) }

        print("草稿箱数据处理 - 当前页码: \(currentPage), 新数据量: \(newItems.count)")

        // 更新数据源
        if isRefresh {
            draftItems = newItems
            originalDraftData = responseData.list // 保存原始数据
            print("草稿箱刷新 - 重置数据，总数: \(draftItems.count)")
        } else {
            draftItems.append(contentsOf: newItems)
            originalDraftData.append(contentsOf: responseData.list) // 追加原始数据
            print("草稿箱加载更多 - 追加数据，总数: \(draftItems.count)")
        }
        
        // 更新分页状态
        hasMoreData = newItems.count >= pageSize
        if !hasMoreData {
            print("草稿箱 - 没有更多数据")
            tableView.mj_footer?.endRefreshingWithNoMoreData()
        } else {
            currentPage += 1
            print("草稿箱 - 更新页码: \(currentPage)")
        }
        
        // 更新分段控件标题
        updateSegmentTitles()
        
        // 刷新表格
        tableView.reloadData()
        
        // 更新占位视图
        updatePlaceholderView()

        if currentPage == 0 || draftItems.count < pageSize {
            tableView.mj_footer?.isHidden = true
        } else {
            tableView.mj_footer?.isHidden = false
            if !hasMoreData {
                tableView.mj_footer?.endRefreshingWithNoMoreData()
            }
        }
    }
    
    /// 格式化时长
    private func formatDuration(seconds: Int) -> String {
        let minutes = seconds / 60
        let remainingSeconds = seconds % 60
        return String(format: "%02d:%02d", minutes, remainingSeconds)
    }
    
    /// 格式化时间
    private func formatTimeAgo(timeString: String?) -> String {
        guard let timeString = timeString else { return "未知时间" }
        
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd HH:mm:ss"
        
        if let date = dateFormatter.date(from: timeString) {
            let now = Date()
            let calendar = Calendar.current
            
            let components = calendar.dateComponents([.year, .month, .day, .hour, .minute], from: date, to: now)
            
            if let year = components.year, year > 0 {
                return "\(year)年前"
            }
            if let month = components.month, month > 0 {
                return "\(month)个月前"
            }
            if let day = components.day, day > 0 {
                if day == 1 { return "昨天" }
                return "\(day)天前"
            }
            if let hour = components.hour, hour > 0 {
                return "\(hour)小时前"
            }
            if let minute = components.minute, minute > 0 {
                return "\(minute)分钟前"
            }
            return "刚刚"
        }
        return timeString
    }
    
    // MARK: - UI更新方法
    
    /// 更新用户信息UI
    private func updateUserInfoUI(with userInfo: UserInfo) {
        // 更新头像
        if let url = URL(string: userInfo.avatar) {
            // 使用 Kingfisher 加载头像
            avatarImageView.kf.setImage(
                with: url,
                placeholder: UIImage(named: "default_avatar"),
                options: [
                    .transition(.fade(0.2)),
                    .cacheOriginalImage
                ]
            )
            
            // 同时更新背景图片
            backgroundImageView.kf.setImage(
                with: url,
                placeholder: UIImage(named: "default_avatar"),
                options: [
                    .transition(.fade(0.2)),
                    .cacheOriginalImage
                ]
            )
        } else {
            // 如果 URL 无效，使用默认图片
            avatarImageView.image = UIImage(named: "default_avatar")
            backgroundImageView.image = UIImage(named: "default_avatar")
        }
        
        // 更新用户名
        usernameLabel.text = userInfo.username
        
        // 更新标签
        tagsStackView.arrangedSubviews.forEach { $0.removeFromSuperview() }
        userInfo.tags.forEach { tag in
            let tagView = TagView(text: tag)
            tagsStackView.addArrangedSubview(tagView)
        }
        
        // 根据可用宽度裁剪展示的标签数量
        adjustTagsToFit()
        
        // 更新统计数据
        fansCountView.configure(count: formatNumber(userInfo.stats.publishCount), title: "发布数")
        likesCountView.configure(count: formatNumber(userInfo.stats.playCount), title: "总播放量")
        videoCountView.configure(count: formatNumber(userInfo.stats.likeCount), title: "获赞数")
        followCountView.configure(count: formatNumber(userInfo.stats.fansCount), title: "粉丝数")
        
        // 缓存统计数据，供弹窗使用
        currentUserStats = userInfo.stats
    }
    
    /// 更新已发布列表UI
    private func updatePublishedListUI(with items: [VideoItem]) {
        publishedItems = items
        updateSegmentTitles()
        tableView.reloadData()
    }
    
    /// 更新草稿箱列表UI
    private func updateDraftListUI(with items: [DraftVideoItem]) {
        draftItems = items
        updateSegmentTitles()
        tableView.reloadData()
    }
    
    /// 格式化数字显示
    private func formatNumber(_ number: Int) -> String {
        if number >= 10000 {
            let value = Double(number) / 10000.0
            return String(format: "%.1f万", value)
        } else if number >= 1000 {
            let value = Double(number) / 1000.0
            return String(format: "%.1f千", value)
        } else {
            return "\(number)"
        }
    }
    
    /// 处理错误
    private func handleError(_ error: Error) {
        // TODO: 实现错误处理
        // 可以显示错误提示，或者重试逻辑
        print("Error: \(error.localizedDescription)")
    }
    
    // 创建渐变背景按钮的辅助方法
    private func createGradientButton(title: String, iconName: String, startColor: UIColor, endColor: UIColor) -> UIButton {
        let button = UIButton(type: .custom)
        button.backgroundColor = .clear // 背景色设为透明，将使用渐变层
        button.layer.cornerRadius = 12
        button.layer.masksToBounds = true
        
        // 创建渐变层
        let gradientLayer = CAGradientLayer()
        gradientLayer.colors = [startColor.cgColor, endColor.cgColor]
        gradientLayer.startPoint = CGPoint(x: 0, y: 0.5)
        gradientLayer.endPoint = CGPoint(x: 1, y: 0.5)
        gradientLayer.locations = [0, 1]
        gradientLayer.cornerRadius = 12
        button.layer.insertSublayer(gradientLayer, at: 0)
        
        // 创建图标视图
        let iconView = UIImageView()
        if let image = UIImage(named: iconName) {
            iconView.image = image
        } else {
            // 使用占位图标
            iconView.backgroundColor = .white
            iconView.layer.cornerRadius = 4
        }
        iconView.contentMode = .scaleAspectFit
        
        // 创建标题标签
        let titleLabel = UILabel()
        titleLabel.text = title
        titleLabel.textColor = .white
        titleLabel.font = UIFont.boldSystemFont(ofSize: 13)
        titleLabel.textAlignment = .left
        
        // 添加子视图
        button.addSubview(iconView)
        button.addSubview(titleLabel)
        
        // 设置约束
        iconView.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(12)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(24)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.left.equalTo(iconView.snp.right).offset(6)
            make.centerY.equalToSuperview()
            make.right.lessThanOrEqualToSuperview().offset(-8)
        }
        
        return button
    }
    
    // MARK: - 生命周期方法
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        // 设置标题（虽然会被隐藏）
        navTitle = "创作中心"
        
        // 确保不会触发TabBar的显示/隐藏
        isTabBarRootViewController = false
        
        // 隐藏基类的导航栏
        navBar.isHidden = true
        
        contentView.backgroundColor = UIColor(hex: "#F5F5F5")
        
        // 设置UI
        setupUI()
        
        // 设置刷新控件
        setupRefreshControl()
        
        // 重置分页状态
        currentPage = 0
        hasMoreData = true
        
        // 加载数据
        fetchUserInfo()
        // 初始化时都从第一页开始加载，以获取正确的总数
        fetchPublishedList(isRefresh: true)
        fetchDraftList(isRefresh: true)
    }
    
    // MARK: - 私有方法
    
    private func setupUI() {
        // 设置视图背景色
        view.backgroundColor = UIColor(hex: "#F5F5F5")
        
        // 将 TableView 添加到主视图
        view.addSubview(tableView)
        tableView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        // 设置 TableView 的背景色
        tableView.backgroundColor = UIColor(hex: "#F5F5F5")
        
        // 隐藏滚动指示器
        tableView.showsVerticalScrollIndicator = false
        
        // 保存原始尺寸
        headerViewOriginalHeight = tableHeaderView.frame.height
        backgroundImageOriginalWidth = UIScreen.main.bounds.width
        
        // 设置 TableView
        tableView.bounces = true
        tableView.alwaysBounceVertical = true
        
        // 将背景图片容器添加到 HeaderView
        tableHeaderView.addSubview(backgroundImageContainer)
        // 设置背景图片容器的约束，允许向上超出
        backgroundImageContainer.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(-backgroundImageHeight) // 向上偏移一个图片高度
            make.left.right.equalToSuperview()
            make.height.equalTo(backgroundImageHeight * 2) // 高度设为两倍，确保有足够空间展示放大效果
        }
        
        backgroundImageContainer.addSubview(backgroundImageView)
        backgroundImageView.frame = CGRect(
            x: 0,
            y: backgroundImageHeight, // 初始位置在容器中间
            width: UIScreen.main.bounds.width,
            height: backgroundImageHeight
        )
        
        // 将渐变蒙版添加到背景图片容器
        backgroundImageContainer.addSubview(overlayView)
        overlayView.frame = backgroundImageView.frame
        
        // 将内容卡片添加到 HeaderView
        tableHeaderView.addSubview(contentCardView)
        contentCardView.snp.makeConstraints { make in
            // 这里的 top 偏移量需要计算好，相对于 HeaderView 顶部
            // 假设导航栏+状态栏高度为 safeAreaInsets.top + 44
            let topOffset = (view.safeAreaInsets.top > 0 ? view.safeAreaInsets.top : UIApplication.shared.statusBarFrame.height) + 44 + 64
            make.top.equalToSuperview().offset(topOffset)
            make.left.equalToSuperview().offset(20)
            make.right.equalToSuperview().offset(-20)
            make.height.equalTo(186)
        }

        // 将头像框和头像添加到 HeaderView
        tableHeaderView.addSubview(avatarGradientFrameView)
        avatarGradientFrameView.snp.makeConstraints { make in
            make.centerX.equalTo(contentCardView)
            make.top.equalTo(contentCardView.snp.top).offset(-42) // 基于 contentCardView 定位
            make.width.height.equalTo(84)
        }
        avatarGradientFrameView.addSubview(avatarImageView)
        avatarImageView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.width.height.equalTo(80)
        }

        // 将内容卡片内的元素添加到 HeaderView (相对于 contentCardView)
        tableHeaderView.addSubview(usernameLabel)
        usernameLabel.snp.makeConstraints { make in
            make.top.equalTo(avatarGradientFrameView.snp.bottom).offset(8)
            make.centerX.equalTo(contentCardView)
            make.height.equalTo(18)
        }

        // 添加标签 StackView
        tableHeaderView.addSubview(tagsStackView)
        tagsStackView.snp.makeConstraints { make in
            make.top.equalTo(usernameLabel.snp.bottom).offset(10) // 调整与用户名的间距
            make.centerX.equalTo(contentCardView)
            // 宽度可以根据内容自适应，但为了防止过宽，可以设置一个相对于 contentCardView 的最大宽度
            make.left.greaterThanOrEqualTo(contentCardView.snp.left).offset(20)
            make.right.lessThanOrEqualTo(contentCardView.snp.right).offset(-20)
            // 高度由内部 TagView 决定，StackView 会自适应
        }

        tableHeaderView.addSubview(statsContainer)
        statsContainer.snp.makeConstraints { make in
            make.top.equalTo(tagsStackView.snp.bottom).offset(15) // 调整与标签的间距
            make.centerX.equalTo(contentCardView)
            make.height.equalTo(50)
        }

        // 创建水平堆叠视图，用于排列统计项 (这部分逻辑不变，只是父视图变为 tableHeaderView 下的 statsContainer)
        let statsStackView = UIStackView()
        statsStackView.axis = .horizontal
        statsStackView.distribution = .equalSpacing
        statsStackView.alignment = .center
        statsStackView.spacing = 20
        statsContainer.addSubview(statsStackView)
        statsStackView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        statsStackView.addArrangedSubview(fansCountView)
        statsStackView.addArrangedSubview(likesCountView)
        statsStackView.addArrangedSubview(videoCountView)
        statsStackView.addArrangedSubview(followCountView)
        fansCountView.snp.makeConstraints { make in make.width.equalTo(64); make.height.equalTo(50) }
        likesCountView.snp.makeConstraints { make in make.width.equalTo(64); make.height.equalTo(50) }
        videoCountView.snp.makeConstraints { make in make.width.equalTo(64); make.height.equalTo(50) }
        followCountView.snp.makeConstraints { make in make.width.equalTo(64); make.height.equalTo(50) }

        // 为统计视图添加点击手势
        [fansCountView, likesCountView, videoCountView, followCountView].enumerated().forEach { index, view in
            view.isUserInteractionEnabled = true
            let tap = UITapGestureRecognizer(target: self, action: #selector(handleStatsTapped(_:)))
            tap.name = String(index) // 0-发布 1-播放量 2-获赞 3-粉丝
            view.addGestureRecognizer(tap)
        }

        // 将底部功能按钮区域添加到 HeaderView
        tableHeaderView.addSubview(buttonsContainer)
        buttonsContainer.snp.makeConstraints { make in
            make.top.equalTo(contentCardView.snp.bottom).offset(24)
            make.left.equalToSuperview().offset(6)
            make.right.equalToSuperview().offset(-6)
            make.height.equalTo(44)
        }

        // 使用 UIStackView 和 Spacer Views 来管理按钮布局
        let spacer1 = UIView()
        let spacer2 = UIView()
        let spacer3 = UIView()
        let spacer4 = UIView()

        // 所有 Spacer Views 背景设为透明，以便调试时可以暂时设为其他颜色查看效果
        [spacer1, spacer2, spacer3, spacer4].forEach { $0.backgroundColor = .clear }

        let buttonStackView = UIStackView(arrangedSubviews: [
            spacer1, publishButton, spacer2, contentManageButton, spacer3, ruleCenterButton, spacer4
        ])
        buttonStackView.axis = .horizontal
        buttonStackView.distribution = .fill // 使用 .fill，因为按钮和spacer都有自己的宽度定义
        buttonStackView.alignment = .center

        buttonsContainer.addSubview(buttonStackView)
        buttonStackView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        // 为按钮设置固定宽度
        let buttonWidth: CGFloat = 108
        [publishButton, contentManageButton, ruleCenterButton].forEach { button in
            button.snp.makeConstraints { make in
                make.width.equalTo(buttonWidth)
                make.height.equalTo(44)
            }
        }

        // 让所有 Spacer Views 等宽
        spacer1.snp.makeConstraints { make in
            make.width.equalTo(spacer2)
            make.width.equalTo(spacer3)
            make.width.equalTo(spacer4)
            make.height.equalTo(1) // Spacer 必须有一个非零高度才能在 StackView 中占据空间
        }
        // spacer2, spacer3, spacer4 的宽度约束已通过 spacer1 建立关联
        [spacer2, spacer3, spacer4].forEach { spacer in
            spacer.snp.makeConstraints { make in
                make.height.equalTo(1)
            }
        }

        // 添加自定义分段控件的占位符到 HeaderView
        tableHeaderView.addSubview(segmentPlaceholderView)
        segmentPlaceholderView.snp.makeConstraints { make in
            make.top.equalTo(buttonsContainer.snp.bottom).offset(24)
            make.left.right.equalToSuperview()
            make.height.equalTo(44) // 与 floatingSegmentControl 高度一致
            make.bottom.equalToSuperview().offset(-10) // HeaderView 底部由这个容器决定
        }

        // -- HeaderView 内容添加结束 --

        // 设置 TableView Header
        // 在 viewDidLayoutSubviews 中计算最终高度并设置 frame
        tableView.tableHeaderView = tableHeaderView

        // 配置悬浮的分段控件 (floatingSegmentControl)
        // 使用 UIStackView 来平分布局
        let segmentStackView = UIStackView(arrangedSubviews: [publishedSegmentButton, draftSegmentButton])
        segmentStackView.axis = .horizontal
        segmentStackView.distribution = .fillEqually
        segmentStackView.alignment = .center // 按钮垂直居中

        floatingSegmentControl.addSubview(segmentStackView)
        segmentStackView.snp.makeConstraints { make in
            make.top.bottom.equalToSuperview()
            make.left.equalToSuperview().offset(16)
            make.right.equalToSuperview().offset(-16)
        }

        // 确保按钮文字居中
        publishedSegmentButton.titleLabel?.textAlignment = .center
        draftSegmentButton.titleLabel?.textAlignment = .center

        // 将指示器添加到 floatingSegmentControl (位于 StackView 下方)
        floatingSegmentControl.addSubview(segmentIndicatorView)
        updateSegmentIndicatorConstraints() // 使用一个辅助方法来设置指示器约束

        // 添加悬浮的分段控件到主视图 (tableView之上, customNavBar之下)
        view.insertSubview(floatingSegmentControl, aboveSubview: tableView)
        floatingSegmentControl.snp.makeConstraints { make in
            make.left.right.equalTo(self.view) // 与 contentCardView 对齐宽度和位置
            make.height.equalTo(44) // 固定高度
            // Y 轴位置将在 scrollViewDidScroll 和 viewDidLayoutSubviews 中动态调整
            // 初始 top 约束可以先不设，或者设一个临时值，因为会被frame覆盖
        }

        // 添加自定义导航栏到最上层
        view.addSubview(customNavBar)
        customNavBar.snp.makeConstraints { make in
            // 修改：顶部改为对齐 view.snp.top，覆盖状态栏区域
            make.top.equalTo(view.snp.top)
            make.left.right.equalToSuperview()
            // 修改：高度包含状态栏/安全区 + 44pt
            let topSafeArea = view.safeAreaInsets.top > 0 ? view.safeAreaInsets.top : UIApplication.shared.statusBarFrame.height
            make.height.equalTo(topSafeArea + 44)
        }
        customNavBar.addSubview(customBackBtn)
        customBackBtn.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(16)
            // 修改：底部对齐导航栏底部，并向上偏移一点，使其在 44pt 高度内居中
            make.bottom.equalToSuperview().offset(-10) // 接近 44pt 高度的一半
            make.width.height.equalTo(24)
        }
        customNavBar.addSubview(navTitleLabel)
        navTitleLabel.snp.makeConstraints { make in
            // 修改：centerY 对齐返回按钮，确保它们在底部 44pt 区域内居中
            make.centerY.equalTo(customBackBtn)
            make.centerX.equalToSuperview()
            make.left.greaterThanOrEqualTo(customBackBtn.snp.right).offset(8)
            make.right.lessThanOrEqualToSuperview().offset(-40)
        }

        // 设置表脚，初始宽度为tableView.bounds.width，高度59
        tableView.tableFooterView = tableFooterContainerView
    }
    
    // MARK: - 事件响应方法
    
    @objc private func customBackButtonTapped() {
        navigationController?.popViewController(animated: true)
    }
    
    @objc private func publishButtonTapped() {
        print("发布作品按钮点击")
        // 处理发布作品逻辑
        let vc = VideoRecordViewController()
        navigationController?.pushViewController(vc, animated: true)
    }
    
    @objc private func contentManageButtonTapped() {
        print("内容管理按钮点击")
        // 处理内容管理逻辑
        let vc = ContentManagementViewController()
        navigationController?.pushViewController(vc, animated: true)
    }
    
    @objc private func ruleCenterButtonTapped() {
        print("规则中心按钮点击")
        // 处理规则中心逻辑
        let webView = WebViewController(url: URL(string: "https://gzsxq.com/rule/RuleCenter.html")!,title: " ")
        self.navigationController?.pushViewController(webView, animated: true)
    }

    @objc private func viewAllButtonTapped() {
        print("查看全部按钮点击")
        if currentSegmentIndex == 0 {
            // 已发布，跳转到内容管理页面
            let vc = ContentManagementViewController()
            navigationController?.pushViewController(vc, animated: true)
        } else {
            // 草稿箱，跳转到VideoDraftBoxViewController
            let vc = VideoDraftBoxViewController()
            navigationController?.pushViewController(vc, animated: true)
        }
    }

    // 新增：分段按钮点击事件
    @objc private func segmentButtonTapped(_ sender: UIButton) {
        let selectedIndex = sender.tag
        guard selectedIndex != currentSegmentIndex else { return } // 点击当前选中的按钮，不处理

        currentSegmentIndex = selectedIndex
        updateSegmentControlUI()
        
        // 根据选中的分段重新加载数据
        if currentSegmentIndex == 0 {
            fetchPublishedList()
        } else {
            fetchDraftList()
        }
        
        tableView.reloadData() // 刷新列表数据
        
        // 更新占位视图
        updatePlaceholderView()
    }

    // 新增：更新分段控件 UI
    private func updateSegmentControlUI() {
        let isPublishedSelected = (currentSegmentIndex == 0)

        publishedSegmentButton.setTitleColor(isPublishedSelected ? UIColor(hex: "#FF5936") : UIColor(hex: "#333333"), for: .normal)
        publishedSegmentButton.titleLabel?.font = isPublishedSelected ? UIFont.systemFont(ofSize: 14, weight: .bold) : UIFont.systemFont(ofSize: 14, weight: .bold)

        draftSegmentButton.setTitleColor(!isPublishedSelected ? UIColor(hex: "#FF5936") : UIColor(hex: "#333333"), for: .normal)
        draftSegmentButton.titleLabel?.font = !isPublishedSelected ? UIFont.systemFont(ofSize: 14, weight: .bold) : UIFont.systemFont(ofSize: 14, weight: .bold)

        // 更新指示器位置 (现在是在 floatingSegmentControl 内部)
        updateSegmentIndicatorConstraints() // 调用辅助方法更新约束

        // 添加动画
        UIView.animate(withDuration: 0.3) {
            self.floatingSegmentControl.layoutIfNeeded()
        }
    }

    // 新增：辅助方法 - 更新分段指示器约束
    private func updateSegmentIndicatorConstraints() {
        let targetButton = (currentSegmentIndex == 0) ? publishedSegmentButton : draftSegmentButton
        segmentIndicatorView.snp.remakeConstraints { make in
            make.height.equalTo(2)
            make.width.equalTo(40) // 可以根据需要调整宽度，例如按钮宽度的一半
            make.bottom.equalToSuperview()
            make.centerX.equalTo(targetButton) // 对齐选中按钮的中心
        }
    }

    // 新增：更新分段按钮标题（包含数量）
    private func updateSegmentTitles() {
        publishedSegmentButton.setTitle("已发布 \(publishedItems.count)", for: .normal)
        draftSegmentButton.setTitle("草稿箱 \(draftItems.count)", for: .normal)
    }

    // MARK: - 重写父类方法
    
    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()

        // 更新headerView的frame
        if headerViewOriginalHeight == 0 {
            headerViewOriginalHeight = tableHeaderView.frame.height
            backgroundImageOriginalWidth = UIScreen.main.bounds.width
        }

        // Update TableView Header's height
        let targetSize = CGSize(width: view.bounds.width, height: UIView.layoutFittingCompressedSize.height)
        let calculatedHeight = tableHeaderView.systemLayoutSizeFitting(targetSize, withHorizontalFittingPriority: .required, verticalFittingPriority: .fittingSizeLevel).height

        if abs(tableHeaderView.frame.height - calculatedHeight) > 1 {
             tableHeaderView.frame = CGRect(x: 0, y: 0, width: view.bounds.width, height: calculatedHeight)
             tableView.tableHeaderView = tableHeaderView
        }

        // Gradient updates for avatar, overlay, and buttons (existing code)
        tableHeaderView.layoutIfNeeded() //确保headerView内部布局完成
        if let gradientLayer = avatarGradientFrameView.layer.sublayers?.first as? CAGradientLayer {
            gradientLayer.frame = avatarGradientFrameView.bounds
            
            let maskLayer = CAShapeLayer()
            let path = UIBezierPath(roundedRect: gradientLayer.bounds, cornerRadius: 42)
            let circlePath = UIBezierPath(roundedRect: CGRect(x: 2, y: 2, width: 80, height: 80), cornerRadius: 40)
            path.append(circlePath)
            path.usesEvenOddFillRule = true
            maskLayer.path = path.cgPath
            maskLayer.fillRule = .evenOdd
            gradientLayer.mask = maskLayer
        }
        
        if let gradientLayer = overlayView.layer.sublayers?.first as? CAGradientLayer {
            gradientLayer.frame = overlayView.bounds
        }
        
        for button in [publishButton, contentManageButton, ruleCenterButton] {
            if let gradientLayer = button.layer.sublayers?.first as? CAGradientLayer {
                gradientLayer.frame = button.bounds
            }
        }

        // 调用方法更新悬浮控件位置
        updateFloatingSegmentControlFrame()

        // 动态调整footerView宽度和高度，适配安全区
        let safeArea = view.window?.safeAreaInsets.bottom ?? UIApplication.shared.windows.first?.safeAreaInsets.bottom ?? 0
        let footerHeight: CGFloat = 35 + 12 + 12 + safeArea
        if let footer = tableView.tableFooterView as? CreativeCenterFooterView {
            if footer.frame.width != tableView.bounds.width || abs(footer.bottomSafeArea - safeArea) > 0.5 {
                footer.frame = CGRect(x: 0, y: 0, width: tableView.bounds.width, height: footerHeight)
                footer.bottomSafeArea = safeArea
                footer.setNeedsLayout()
                tableView.tableFooterView = footer
            }
        }

        // 标签在布局变化后重新适配
        adjustTagsToFit()
    }

    // 新增：更新悬浮分段控件的 Y 坐标
    private func updateFloatingSegmentControlFrame() {
        // 确保 customNavBar 已经有正确的 frame
        view.layoutIfNeeded()
        let stickyPointY = customNavBar.frame.maxY
        
        // 计算占位符 segmentPlaceholderView 在 view 中的实时 Y 坐标
        let placeholderFrameInView = tableHeaderView.convert(segmentPlaceholderView.frame, to: view)
        let placeholderTopY = placeholderFrameInView.origin.y
        
        let floatingSegmentY = max(stickyPointY, placeholderTopY)
        
        // 仅当计算出的 Y 与当前 Y 不同时才更新，避免不必要的重绘或动画问题
        if abs(floatingSegmentControl.frame.origin.y - floatingSegmentY) > 0.1 {
            floatingSegmentControl.frame.origin.y = floatingSegmentY
        }
        // 确保宽度和X坐标与约束一致 (如果之前是通过frame设置的，约束可能不会立即反应)
        // 但我们之前为 floatingSegmentControl 设置了左右约束，所以X和宽度应由AutoLayout管理
        // 如果发现X或宽度有问题，可能需要在这里强制更新：
        // floatingSegmentControl.frame.origin.x = contentCardView.frame.origin.x // Assuming alignment with contentCardView
        // floatingSegmentControl.frame.size.width = contentCardView.frame.width
    }

    // MARK: - UIScrollViewDelegate
    func scrollViewDidScroll(_ scrollView: UIScrollView) {
        let offsetY = scrollView.contentOffset.y
        
        // 处理背景图片的缩放效果
        if offsetY < 0 {
            // 计算缩放比例
            let scale = 1.0 - (offsetY / backgroundImageHeight)
            print("缩放比例:\(scale), y:\(offsetY)")
            
            // 计算新的高度和宽度
            let newHeight = backgroundImageHeight * scale
            let newWidth = backgroundImageOriginalWidth * scale
            
            // 计算新的X坐标，使图片保持水平居中
            let newX = (backgroundImageOriginalWidth - newWidth) / 2
            
            // 更新背景图片的frame，保持顶部对齐
            backgroundImageView.frame = CGRect(
                x: newX,
                y: backgroundImageHeight + offsetY, // 从初始位置开始偏移
                width: newWidth,
                height: newHeight
            )
            
            // 更新渐变蒙版的frame，禁用动画
            CATransaction.begin()
            CATransaction.setDisableActions(true)
            overlayView.frame = backgroundImageView.frame
            if let gradientLayer = overlayView.layer.sublayers?.first as? CAGradientLayer {
                gradientLayer.frame = overlayView.bounds
            }
            CATransaction.commit()
            
            // 调整tableHeaderView的高度
            let newHeaderHeight = headerViewOriginalHeight - offsetY
            tableHeaderView.frame.size.height = newHeaderHeight
            tableHeaderView.frame.origin.y = offsetY
        } else {
            // 恢复原始尺寸和位置
            backgroundImageView.frame = CGRect(
                x: 0,
                y: backgroundImageHeight, // 恢复初始位置
                width: backgroundImageOriginalWidth,
                height: backgroundImageHeight
            )
            
            // 恢复渐变蒙版的frame，禁用动画
            CATransaction.begin()
            CATransaction.setDisableActions(true)
            overlayView.frame = backgroundImageView.frame
            if let gradientLayer = overlayView.layer.sublayers?.first as? CAGradientLayer {
                gradientLayer.frame = overlayView.bounds
            }
            CATransaction.commit()
            
            tableHeaderView.frame.size.height = headerViewOriginalHeight
            tableHeaderView.frame.origin.y = 0
        }
        
        // 定义渐变开始和结束的偏移量
        let transitionStartOffset: CGFloat = 0
        let transitionEndOffset: CGFloat = 434
        
        var alpha: CGFloat = 0
        if offsetY <= transitionStartOffset {
            alpha = 0
        } else if offsetY >= transitionEndOffset {
            alpha = 1
        } else {
            alpha = (offsetY - transitionStartOffset) / (transitionEndOffset - transitionStartOffset)
        }
        
        alpha = min(1, max(0, alpha))
        
        customNavBar.backgroundColor = UIColor.white.withAlphaComponent(alpha)
        
        // 调用方法更新悬浮控件位置
        updateFloatingSegmentControlFrame()
    }

    // MARK: - 设置刷新控件
    private func setupRefreshControl() {
        // 设置下拉刷新
        let header = MJRefreshNormalHeader { [weak self] in
            guard let self = self else { return }
            // 刷新用户信息
            self.fetchUserInfo()
            // 根据当前选中的分段刷新对应的列表
            if self.currentSegmentIndex == 0 {
                self.fetchPublishedList(isRefresh: true)
            } else {
                self.fetchDraftList(isRefresh: true)
            }
        }
        header.setTitle("下拉刷新", for: .idle)
        header.setTitle("释放刷新", for: .pulling)
        header.setTitle("正在刷新", for: .refreshing)
        tableView.mj_header = header
        
        // 取消上拉加载更多，仅展示前 maxDisplayCount 条
        tableView.mj_footer = nil
    }

    // MARK: - CreativeEmptyPlaceholderView 占位视图
    class CreativeEmptyPlaceholderView: UIView {
        let imageView = UIImageView()
        let messageLabel = UILabel()
        let actionButton = UIButton(type: .custom)
        var actionHandler: (() -> Void)?
        
        override init(frame: CGRect) {
            super.init(frame: frame)
            setupUI()
        }
        
        required init?(coder: NSCoder) {
            super.init(coder: coder)
            setupUI()
        }
        
        private func setupUI() {
            backgroundColor = .clear
            
            imageView.image = UIImage(named: "empty_data_placeholder_image")
            imageView.contentMode = .scaleAspectFit
            imageView.clipsToBounds = true
            addSubview(imageView)
            
            messageLabel.text = "暂时没有作品"
            messageLabel.textColor = UIColor(hex: "#3D3D3D")
            messageLabel.font = UIFont.systemFont(ofSize: 15, weight: .medium)
            messageLabel.textAlignment = .center
            addSubview(messageLabel)
            
            actionButton.setTitle("去发布", for: .normal)
            actionButton.setTitleColor(UIColor(hex: "#3D3D3D"), for: .normal)
            actionButton.titleLabel?.font = UIFont.systemFont(ofSize: 13)
            actionButton.layer.cornerRadius = 14.5
            actionButton.layer.borderWidth = 1
            actionButton.layer.borderColor = UIColor(hex: "#C4C4C4").cgColor
            actionButton.backgroundColor = .clear
            actionButton.addTarget(self, action: #selector(actionTapped), for: .touchUpInside)
            addSubview(actionButton)
            
            // 布局
            imageView.snp.makeConstraints { make in
                make.top.equalToSuperview()
                make.centerX.equalToSuperview()
                make.width.height.equalTo(115)
            }
            messageLabel.snp.makeConstraints { make in
                make.top.equalTo(imageView.snp.bottom).offset(8)
                make.centerX.equalToSuperview()
            }
            actionButton.snp.makeConstraints { make in
                make.top.equalTo(messageLabel.snp.bottom).offset(8)
                make.centerX.equalToSuperview()
                make.width.equalTo(77)
                make.height.equalTo(29)
                make.bottom.equalToSuperview() // 方便整体高度自适应
            }
        }
        
        @objc private func actionTapped() {
            actionHandler?()
        }
    }

    // MARK: - CreativeCenterViewController 占位视图集成
    private var emptyPlaceholderView: CreativeEmptyPlaceholderView?

    private func updatePlaceholderView() {
        // 先移除旧的
        emptyPlaceholderView?.removeFromSuperview()
        emptyPlaceholderView = nil

        let isEmpty = (currentSegmentIndex == 0 ? publishedItems.isEmpty : draftItems.isEmpty)

        // 列表为空则不展示"查看全部"按钮；非空则始终展示
        tableView.tableFooterView = isEmpty ? nil : tableFooterContainerView

        if isEmpty {
            let placeholder = CreativeEmptyPlaceholderView()
            placeholder.actionHandler = { [weak self] in
                self?.publishButtonTapped()
            }
            emptyPlaceholderView = placeholder
            tableView.addSubview(placeholder)

            // 计算Y值：屏幕高度的1/4 + tableView的contentOffset - headerView高度
            let screenHeight = UIScreen.main.bounds.height
            let y = screenHeight * 0.25 + tableView.contentOffset.y
            let headerHeight = tableHeaderView.frame.height

            placeholder.snp.makeConstraints { make in
                make.top.equalTo(tableView).offset(y > headerHeight ? y : headerHeight + 20)
                make.centerX.equalTo(tableView)
                make.width.equalTo(200)
                make.height.equalTo(200)
            }
        }
    }

    // MARK: - 编辑/删除处理
    private func handleEdit(at indexPath: IndexPath) {
        if currentSegmentIndex == 0 {
            // 已发布
            guard indexPath.row < publishedItems.count else { return }
            let item = publishedItems[indexPath.row]
            navigateToEditPage(for: item)
        } else {
            guard indexPath.row < draftItems.count else { return }
            let item = draftItems[indexPath.row]
            navigateToEditPage(for: item)
        }
    }
    
    private func confirmDelete(at indexPath: IndexPath) {
        let alertView = CommonAlertView(
            title: "确认删除",
            message: "确定后，将永久删除该作品",
            leftButtonTitle: "取消",
            rightButtonTitle: "删除"
        )
        alertView.onLeftButtonTap = { [weak alertView] in
            alertView?.dismiss()
        }
        alertView.onRightButtonTap = { [weak self, weak alertView] in
            alertView?.dismiss()
            self?.performDelete(at: indexPath)
        }
        alertView.show(in: self.view)
    }
    
    private func performDelete(at indexPath: IndexPath) {
        // 显示加载指示器
        let loadingHUD = UIActivityIndicatorView(style: .large)
        loadingHUD.color = UIColor(hex: "#FF6236")
        loadingHUD.center = view.center
        view.addSubview(loadingHUD)
        loadingHUD.startAnimating()
        
        if currentSegmentIndex == 0 {
            // 已发布
            let item = publishedItems[indexPath.row]
            APIManager.shared.shortVideoWorksOperate(type: 3, worksIds: [item.id]) { [weak self] result in
                DispatchQueue.main.async {
                    loadingHUD.removeFromSuperview()
                    self?.handleDeleteResponse(result: result, indexPath: indexPath)
                }
            }
        } else {
            // 草稿箱
            let item = draftItems[indexPath.row]
            guard let idInt = Int(item.id) else {
                loadingHUD.removeFromSuperview()
                self.showToast("删除失败，ID无效")
                return
            }
            APIManager.shared.deleteVideoWorksDrafts(ids: [idInt]) { [weak self] result in
                DispatchQueue.main.async {
                    loadingHUD.removeFromSuperview()
                    self?.handleDeleteResponse(result: result, indexPath: indexPath)
                }
            }
        }
    }
    
    private func handleDeleteResponse(result: Result<BaseResponse, APIError>, indexPath: IndexPath) {
        switch result {
        case .success(_):
            if currentSegmentIndex == 0 {
                publishedItems.remove(at: indexPath.row)
            } else {
                draftItems.remove(at: indexPath.row)
            }
            updateSegmentTitles()
            tableView.deleteRows(at: [indexPath], with: .fade)
            updatePlaceholderView()
            self.showToast("删除成功")
        case .failure(let error):
            self.showToast("删除失败: \(error.localizedDescription)")
        }
    }

    // MARK: - 跳转到对应编辑页
    private func navigateToEditPage(for item: VideoItem) {
        if item.worksType == 2 {
            // 笔记编辑
            let editVC = NoteEditingDetailsViewController(images: nil)
            navigationController?.pushViewController(editVC, animated: true)
            return
        }

        // === 视频编辑 ===
        // 构造预填充信息
        let prefill = VideoPrefillInfo(
            worksId: item.id,
            title: item.title,
            categoryId: item.categoryId,
            categoryName: item.categoryName,
            privacy: item.privacy,
            allowComment: (item.allowComment ?? 0) == 1,
            followComment: (item.followComment ?? 0) == 1,
            latitude: Double(item.lat ?? ""),
            longitude: Double(item.lng ?? ""),
            areaCode: nil,
            address: item.address,
            scheduleTime: item.extValue
        )

        // 尝试异步下载封面
        if let url = URL(string: item.thumbnail) {
            KingfisherManager.shared.retrieveImage(with: url) { [weak self] result in
                guard let self = self else { return }
                var cover: UIImage? = nil
                if case let .success(value) = result {
                    cover = value.image
                }
                let vc = VideoEditingDetailsViewController(
                    videoPath: "",                // 已发布视频仅修改元数据，无需重新上传
                    coverImage: cover,
                    videoDuration: item.durationSeconds != nil ? CGFloat(item.durationSeconds!) : nil,
                    videoSize: UInt64(item.size),
                    prefillInfo: prefill
                )
                self.navigationController?.pushViewController(vc, animated: true)
            }
        } else {
            // 无有效封面 URL，直接跳转
            let vc = VideoEditingDetailsViewController(
                videoPath: "",
                coverImage: nil,
                videoDuration: item.durationSeconds != nil ? CGFloat(item.durationSeconds!) : nil,
                videoSize: UInt64(item.size),
                prefillInfo: prefill
            )
            navigationController?.pushViewController(vc, animated: true)
        }
    }

    private func navigateToEditPage(for item: DraftVideoItem) {
        // 查找对应的原始数据
        guard let originalData = originalDraftData.first(where: { String($0.id) == item.id }) else {
            print("未找到草稿的原始数据，ID: \(item.id)")
            return
        }

        if item.worksType == 2 {
            // 笔记类型，进入笔记编辑页面
            // 解析worksUrl获取图片URL数组
            var imageUrls: [String] = []
            if !originalData.worksUrl.isEmpty {
                if let data = originalData.worksUrl.data(using: .utf8),
                   let urlArray = try? JSONSerialization.jsonObject(with: data, options: []) as? [String] {
                    imageUrls = urlArray
                } else {
                    imageUrls = [originalData.worksUrl] // 如果解析失败，直接作为单个URL
                }
            }

            // 异步加载图片
            let group = DispatchGroup()
            var loadedImages: [UIImage] = []

            for urlString in imageUrls {
                group.enter()
                if let url = URL(string: urlString) {
                    let imageView = UIImageView()
                    imageView.kf.setImage(with: url) { result in
                        switch result {
                        case .success(let value):
                            loadedImages.append(value.image)
                        case .failure:
                            print("加载图片失败: \(urlString)")
                        }
                        group.leave()
                    }
                } else {
                    group.leave()
                }
            }

            group.notify(queue: .main) { [weak self] in
                let editVC = NoteEditingDetailsViewController(images: loadedImages.isEmpty ? nil : loadedImages)
                editVC.draftsId = Int(item.id) ?? 0 // 传入草稿ID
                editVC.isEditMode = true // 设置为编辑模式
                editVC.setOriginalImageUrls(imageUrls) // 传递原始图片URL
                self?.navigationController?.pushViewController(editVC, animated: true)
            }
        } else {
            // 视频类型，进入视频发布页面
            // 使用原始数据构造完整的预填充信息
            let prefill = VideoPrefillInfo(
                worksId: item.id, // 使用草稿ID作为worksId
                title: originalData.worksTitle.isEmpty ? nil : originalData.worksTitle,
                categoryId: originalData.worksCategoryId == 0 ? nil : originalData.worksCategoryId,
                categoryName: originalData.worksCategoryName,
                privacy: originalData.privacy,
                allowComment: originalData.allowComment == 1,
                followComment: originalData.followComment == 1,
                latitude: Double(originalData.lat ?? ""),
                longitude: Double(originalData.lng ?? ""),
                areaCode: nil, // PersonalWorksListDetailData没有areaCode字段
                address: originalData.address.isEmpty ? nil : originalData.address,
                scheduleTime: originalData.extValue,
                worksCoverImg: originalData.worksCoverImg,
                worksUrl: originalData.worksUrl,
                videoId: originalData.videoId,
                worksDescribe: originalData.worksDescribe
            )

            // 处理封面URL
            var coverImageUrl = item.thumbnail
            if !coverImageUrl.isEmpty && !coverImageUrl.hasPrefix("http") {
                coverImageUrl = "https://test-youshu.gzyoushu.com/video" + coverImageUrl
            }

            if !coverImageUrl.isEmpty, let url = URL(string: coverImageUrl) {
                // 异步加载封面图片
                let imageView = UIImageView()
                imageView.kf.setImage(with: url) { [weak self] result in
                    DispatchQueue.main.async {
                        var coverImage: UIImage?
                        switch result {
                        case .success(let value):
                            coverImage = value.image
                        case .failure:
                            coverImage = nil
                        }

                        let editVC = VideoEditingDetailsViewController(
                            videoPath: item.worksUrl,
                            coverImage: coverImage,
                            videoDuration: nil,
                            videoSize: UInt64(item.size),
                            prefillInfo: prefill
                        )
                        editVC.draftsId = Int(item.id) ?? 0 // 传入草稿ID
                        editVC.isDraftEditMode = true // 设置为草稿编辑模式
                        self?.navigationController?.pushViewController(editVC, animated: true)
                    }
                }
            } else {
                // 无封面URL，直接跳转
                let editVC = VideoEditingDetailsViewController(
                    videoPath: item.worksUrl,
                    coverImage: nil,
                    videoDuration: nil,
                    videoSize: UInt64(item.size),
                    prefillInfo: prefill
                )
                editVC.draftsId = Int(item.id) ?? 0 // 传入草稿ID
                editVC.isDraftEditMode = true // 设置为草稿编辑模式
                navigationController?.pushViewController(editVC, animated: true)
            }
        }
    }

    // MARK: - 统计视图点击弹窗处理
    @objc private func handleStatsTapped(_ sender: UITapGestureRecognizer) {
        guard let name = sender.name, let index = Int(name), let stats = currentUserStats else { return }
        var message: String = ""
        switch index {
        case 0:
            message = "当前共发布\(stats.publishCount)个作品"
            InfoPopupView.show(in: self.view, title: nil, message: message)
        case 1:
            message = "当前共获得\(formatCountWithWanIfNeeded(stats.playCount))播放量"
            InfoPopupView.show(in: self.view, title: nil, message: message)
        case 2:
            message = "共获得\(formatCountWithWanIfNeeded(stats.likeCount))点赞"
            InfoPopupView.show(in: self.view, title: nil, message: message)
        case 3:
            // 跳转到粉丝列表，与个人中心一致
            let followListVC = FollowListViewController()
            followListVC.selectedSegmentIndex = 1 // 1 表示粉丝
            navigationController?.pushViewController(followListVC, animated: true)
        default:
            break
        }
    }
    
    // MARK: - Formatter
    private func formatCountWithWanIfNeeded(_ number: Int) -> String {
        if number >= 10000 {
            let value = Double(number) / 10000.0
            return String(format: "%.1f万", value)
        }
        return "\(number)"
    }

    // MARK: - 当前用户统计缓存 (供弹窗使用)
    private var currentUserStats: UserStats?

    // MARK: - 标签宽度适配
    /// 根据 `contentCardView` 的可用宽度裁剪标签，仅保留完全可见的标签
    private func adjustTagsToFit() {
        // 先确保布局完成，获取到准确的尺寸
        view.layoutIfNeeded()

        // 计算可用宽度：contentCardView 的宽度减去其左右内边距(与 tagsStackView 约束的 20)
        let maxWidth = contentCardView.bounds.width - 40
        guard maxWidth > 0 else { return }

        var currentWidth: CGFloat = 0
        var validViews: [UIView] = []

        // 遍历 arrangedSubviews，判断累加宽度是否超出
        for (index, subview) in tagsStackView.arrangedSubviews.enumerated() {
            // 需要先取得子视图的最终尺寸
            let subviewWidth = subview.systemLayoutSizeFitting(UIView.layoutFittingCompressedSize).width
            let spacing = index == 0 ? 0 : tagsStackView.spacing
            if currentWidth + spacing + subviewWidth <= maxWidth {
                currentWidth += spacing + subviewWidth
                validViews.append(subview)
            } else {
                break // 超出可用宽度，后续全部舍弃
            }
        }

        // 将不在 validViews 中的视图移除
        for view in tagsStackView.arrangedSubviews {
            if !validViews.contains(view) {
                tagsStackView.removeArrangedSubview(view)
                view.removeFromSuperview()
            }
        }
    }

    // 工具：解析后端字符串或JSON数组形式的 worksUrl
    func parseWorksUrls(_ raw: String) -> [String] {
        // 1. 尝试解析为 JSON 数组
        if let data = raw.data(using: .utf8) {
            if let arr = try? JSONSerialization.jsonObject(with: data, options: []) as? [String], !arr.isEmpty {
                return arr
            }
        }
        // 2. 尝试按逗号/空格分割
        let separators = CharacterSet(charactersIn: ",|; ")
        let parts = raw.components(separatedBy: separators).filter { !$0.isEmpty }
        if !parts.isEmpty {
            return parts
        }
        // 3. 直接返回单元素数组
        return raw.isEmpty ? [] : [raw]
    }
}

// MARK: - TagView Class (Helper for displaying individual tags)
class TagView: UIView {
    private let label = UILabel()

    init(text: String) {
        super.init(frame: .zero)
        backgroundColor = UIColor(hex: "#CCCCCC", alpha: 0.8) // 浅灰色背景，如图所示
        layer.cornerRadius = 12 // 根据图片调整圆角，使其看起来是胶囊状，可能需要高度的一半
        layer.masksToBounds = true

        label.text = text
        label.font = UIFont.systemFont(ofSize: 12, weight: .semibold) // 字体大小
        label.textColor = .white
        label.textAlignment = .center

        addSubview(label)
        label.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(4)    // 垂直内边距
            make.bottom.equalToSuperview().offset(-4) // 垂直内边距
            make.left.equalToSuperview().offset(10)   // 水平内边距
            make.right.equalToSuperview().offset(-10)  // 水平内边距
        }
        // 自动调整高度以适应内容
        self.snp.makeConstraints { make in
             make.height.greaterThanOrEqualTo(20) // 最小高度，确保圆角效果
        }
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // 动态调整圆角以保持胶囊形状
    override func layoutSubviews() {
        super.layoutSubviews()
        layer.cornerRadius = bounds.height / 2
    }
}

// MARK: - CreativeCenterStatsView 类
// 将类名从 StatsView 改为 CreativeCenterStatsView，避免歧义
class CreativeCenterStatsView: UIView {
    private let countLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 16, weight: .bold)
        label.textColor = UIColor(hex: "#333333")
        label.textAlignment = .center
        return label
    }()
    
    private let titleLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 12)
        label.textColor = UIColor(hex: "#777777")
        label.textAlignment = .center
        return label
    }()
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupUI() {
        addSubview(countLabel)
        addSubview(titleLabel)
        
        countLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(2)
            make.centerX.equalToSuperview()
            make.left.right.equalToSuperview()
            make.height.equalTo(20)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.top.equalTo(countLabel.snp.bottom).offset(0)
            make.centerX.equalToSuperview()
            make.left.right.equalToSuperview()
            make.height.equalTo(16)
            make.bottom.equalToSuperview().offset(-2)
        }
    }
    
    func configure(count: String, title: String) {
        countLabel.text = count
        titleLabel.text = title
    }
}

// MARK: - 新增 UITableViewDelegate & UITableViewDataSource
extension CreativeCenterViewController: UITableViewDelegate, UITableViewDataSource {

    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        // 仅在创作中心页面展示最多 maxDisplayCount 条，超过的通过"查看全部"进入完整列表
        let total = currentSegmentIndex == 0 ? publishedItems.count : draftItems.count
        return min(total, maxDisplayCount)
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: "CreativeVideoItemCell", for: indexPath) as! CreativeVideoItemCell

        if currentSegmentIndex == 0 {
            let item = publishedItems[indexPath.row]
            cell.configure(
                title: item.title,
                duration: item.duration,
                timeAgo: item.timeAgo,
                thumbnail: item.thumbnail,
                sizeInBytes: item.size,
                showDuration: item.worksType != 2
            )
        } else {
            let item = draftItems[indexPath.row]
            cell.configure(
                title: item.title,
                duration: item.duration,
                timeAgo: item.timeAgo,
                thumbnail: item.thumbnail,
                sizeInBytes: item.size,
                showDuration: item.worksType != 2
            )
            cell.editButton.isHidden = false
        }
        
        // 设置编辑/删除按钮回调
        cell.onEditTapped = { [weak self, weak cell] in
            guard let self = self,
                  let cell = cell,
                  let indexPath = tableView.indexPath(for: cell) else { return }
            self.handleEdit(at: indexPath)
        }
        
        cell.onDeleteTapped = { [weak self, weak cell] in
            guard let self = self,
                  let cell = cell,
                  let indexPath = tableView.indexPath(for: cell) else { return }
            self.confirmDelete(at: indexPath)
        }
        
        return cell
    }

    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 135 // 和之前保持一致
    }

    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        print("点击了 \(currentSegmentIndex == 0 ? "已发布" : "草稿箱") 列表的第 \(indexPath.row) 行")
        if currentSegmentIndex == 0 {
            // 已发布：跳转视频播放页，使用我的作品池
            guard indexPath.row < publishedItems.count else { return }

            let localItem = publishedItems[indexPath.row]

            // 如果是笔记类型，需要先获取作品详情再跳转，避免数据缺失
            if localItem.worksType == 2 {
                guard let worksId = Int(localItem.id) else {
                    self.showToast("作品ID无效")
                    return
                }

                // 显示加载指示器
                let loadingHUD = UIActivityIndicatorView(style: .large)
                loadingHUD.center = view.center
                view.addSubview(loadingHUD)
                loadingHUD.startAnimating()

                APIManager.shared.getVideoDetail(videoId: worksId) { [weak self] result in
                    DispatchQueue.main.async {
                        loadingHUD.removeFromSuperview()
                        guard let self = self else { return }

                        switch result {
                        case .success(let response):
                            guard var noteVideo = response.data else {
                                self.showToast("获取作品详情失败")
                                return
                            }
                            // 标记为我的作品，播放器中隐藏关注
                            noteVideo.isMyWorks = true

                            // 如果作者名称缺失，则补充页面头部展示的用户名
                            if noteVideo.svUserMainVo == nil {
                                noteVideo.svUserMainVo = SvUserMainVo(collect: nil,
                                                                        customerId: nil,
                                                                        customerName: self.usernameLabel.text,
                                                                        fansNumber: nil,
                                                                        follow: nil,
                                                                        like: nil,
                                                                        state: nil,
                                                                        wxAvator: nil)
                            } else if noteVideo.svUserMainVo?.customerName == nil ||
                                        noteVideo.svUserMainVo?.customerName?.isEmpty == true {
                                noteVideo.svUserMainVo?.customerName = self.usernameLabel.text
                            }

                            let vc = VideoDisplayCenterViewController(videoList: [noteVideo])
                            self.navigationController?.pushViewController(vc, animated: true)
                        case .failure(let error):
                            self.showToast(error.localizedDescription)
                        }
                    }
                }
                tableView.deselectRow(at: indexPath, animated: true)
                return
            }

            // 否则为视频类型：先请求详情，获取完整播放参数
            guard let worksId = Int(localItem.id) else {
                self.showToast("作品ID无效")
                return
            }

            // 显示加载指示器
            let loadingHUD = UIActivityIndicatorView(style: .large)
            loadingHUD.center = view.center
            view.addSubview(loadingHUD)
            loadingHUD.startAnimating()

            APIManager.shared.getVideoDetail(videoId: worksId) { [weak self] result in
                DispatchQueue.main.async {
                    loadingHUD.removeFromSuperview()
                    guard let self = self else { return }

                    switch result {
                    case .success(let response):
                        guard let videoItem = response.data else {
                            self.showToast("获取作品详情失败")
                            return
                        }

                        // 标记为我的作品，播放器中隐藏关注
                        var myVideo = videoItem
                        myVideo.isMyWorks = true

                        let vc = VideoDisplayCenterViewController(videoList: [myVideo])
                        self.navigationController?.pushViewController(vc, animated: true)
                    case .failure(let error):
                        self.showToast(error.localizedDescription)
                    }
                }
            }

            tableView.deselectRow(at: indexPath, animated: true)
            return
        }

        // 草稿箱：点击整行进入编辑页面
        if currentSegmentIndex == 1 {
            // 草稿箱
            guard indexPath.row < draftItems.count else { return }
            let item = draftItems[indexPath.row]
            navigateToEditPage(for: item)
        }
        tableView.deselectRow(at: indexPath, animated: true)
    }
}

// MARK: - CreativeCenterFooterView 表脚视图
class CreativeCenterFooterView: UIView {
    let viewAllButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setTitle("查看全部", for: .normal)
        button.setTitleColor(UIColor(hex: "#FF6236"), for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 13)
        button.backgroundColor = .clear
        button.layer.cornerRadius = 17
        button.layer.borderWidth = 1
        button.layer.borderColor = UIColor(hex: "#FF6236").cgColor
        if let iconImage = UIImage(named: "icon_arrow_right") {
            button.setImage(iconImage, for: .normal)
            button.semanticContentAttribute = .forceRightToLeft
            button.imageEdgeInsets = UIEdgeInsets(top: 0, left: 6, bottom: 0, right: 0)
        }
        return button
    }()
    var bottomSafeArea: CGFloat = 0
    init(frame: CGRect, bottomSafeArea: CGFloat) {
        self.bottomSafeArea = bottomSafeArea
        super.init(frame: frame)
        backgroundColor = .clear
        addSubview(viewAllButton)
    }
    override func layoutSubviews() {
        super.layoutSubviews()
        // 按钮宽115，高35，圆角17，上下间距12，底部加安全区
        let buttonWidth: CGFloat = 115
        let buttonHeight: CGFloat = 35
        let topMargin: CGFloat = 12
        let bottomMargin: CGFloat = 12 + bottomSafeArea
        let x = (bounds.width - buttonWidth) / 2
        let y = topMargin
        viewAllButton.frame = CGRect(x: x, y: y, width: buttonWidth, height: buttonHeight)
    }
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
}
