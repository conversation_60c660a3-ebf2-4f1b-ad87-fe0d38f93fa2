//
//  VideoDraftBoxViewController.swift
//  Shu<PERSON><PERSON>qi
//
//  Created by yongsheng ye on 2025/3/24.
//

//  视频草稿箱

import UIKit
import SnapKit
import MJRefresh
import Kingfisher
import MBProgressHUD

class VideoDraftBoxViewController: BaseViewController {
    
    // MARK: - UI 组件
    
    // 草稿数量标签
    private lazy var countLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 14)
        label.textColor = UIColor(hex: "#999999")
        label.text = "共0个草稿"
        return label
    }()
    
    // 右上角管理按钮
    private lazy var manageButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setTitle("管理", for: .normal)
        button.setTitle("完成", for: .selected)
        button.setTitleColor(UIColor(hex: "#FF6236"), for: .normal)
        button.setTitleColor(UIColor(hex: "#FF6236"), for: .selected)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 14)
        button.addTarget(self, action: #selector(manageButtonTapped), for: .touchUpInside)
        return button
    }()
    
    // 自定义搜索容器视图（渐变边框）
    private lazy var searchContainer: UIView = {
        let container = UIView()
        container.backgroundColor = .white
        container.layer.cornerRadius = 20
        return container
    }()
    
    // 渐变边框层
    private lazy var gradientBorder: CAGradientLayer = {
        let gradient = CAGradientLayer()
        gradient.colors = [
            UIColor(hex: "#FF8D36").cgColor,
            UIColor(hex: "#FF6236").cgColor,
            UIColor(hex: "#FF3B74").cgColor
        ]
        gradient.startPoint = CGPoint(x: 0, y: 0.5)
        gradient.endPoint = CGPoint(x: 1, y: 0.5)
        gradient.locations = [0, 0.5, 1]
        gradient.cornerRadius = 20
        
        // 使用遮罩创建边框效果
        let shape = CAShapeLayer()
        shape.lineWidth = 1
        shape.path = UIBezierPath(roundedRect: CGRect(x: 0.5, y: 0.5, width: UIScreen.main.bounds.width - 33, height: 39), cornerRadius: 20).cgPath
        shape.strokeColor = UIColor.black.cgColor
        shape.fillColor = UIColor.clear.cgColor
        gradient.mask = shape
        
        return gradient
    }()
    
    // 搜索图标
    private lazy var searchIcon: UIImageView = {
        let imageView = UIImageView(image: UIImage(named: "icon_search")?.withRenderingMode(.alwaysTemplate))
        imageView.tintColor = UIColor(hex: "#999999")
        imageView.contentMode = .scaleAspectFit
        return imageView
    }()
    
    // 搜索输入框
    private lazy var searchTextField: UITextField = {
        let textField = UITextField()
        textField.placeholder = "搜索草稿"
        textField.font = UIFont.systemFont(ofSize: 14)
        textField.textColor = UIColor(hex: "#333333")
        textField.returnKeyType = .search
        textField.clearButtonMode = .whileEditing
        textField.delegate = self
        
        // 设置placeholder颜色
        let placeholderAttributes: [NSAttributedString.Key: Any] = [
            .foregroundColor: UIColor(hex: "#999999"),
            .font: UIFont.systemFont(ofSize: 14)
        ]
        textField.attributedPlaceholder = NSAttributedString(string: "搜索草稿", attributes: placeholderAttributes)
        
        return textField
    }()
    
    // 视频列表
    private lazy var collectionView: UICollectionView = {
        let collectionView = UICollectionView(
            frame: .zero,
            collectionViewLayout: createCompositionalLayout()
        )
        collectionView.backgroundColor = UIColor(hex: "#F5F5F5")
        collectionView.delegate = self
        collectionView.dataSource = self
        collectionView.register(DraftVideoCollectionCell.self, forCellWithReuseIdentifier: "DraftVideoCollectionCell")
        collectionView.showsVerticalScrollIndicator = false
        collectionView.allowsMultipleSelection = true
        return collectionView
    }()
    
    // 底部编辑操作视图
    private lazy var editActionsView: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        view.isHidden = true
        
        // 添加分割线
        let separator = UIView()
        separator.backgroundColor = UIColor(hex: "#EEEEEE")
        view.addSubview(separator)
        separator.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.height.equalTo(0.5)
        }
        
        // 添加全选按钮
        let selectAllBtn = UIButton(type: .custom)
        selectAllBtn.setImage(UIImage(named: "video_allbtn_default")?.withRenderingMode(.alwaysOriginal), for: .normal)
        selectAllBtn.setImage(UIImage(named: "video_allbtn_selected")?.withRenderingMode(.alwaysOriginal), for: .selected)
        selectAllBtn.setTitle("全选", for: .normal)
        selectAllBtn.setTitleColor(UIColor(hex: "#333333"), for: .normal)
        selectAllBtn.titleLabel?.font = UIFont.systemFont(ofSize: 14)
        selectAllBtn.addTarget(self, action: #selector(selectAllButtonTapped(_:)), for: .touchUpInside)
        selectAllBtn.contentHorizontalAlignment = .left
        selectAllBtn.imageEdgeInsets = UIEdgeInsets(top: 0, left: 0, bottom: 0, right: 0)
        selectAllBtn.titleEdgeInsets = UIEdgeInsets(top: 0, left: 8, bottom: 0, right: -8)
        selectAllBtn.contentEdgeInsets = UIEdgeInsets(top: 0, left: 0, bottom: 0, right: 8)
        view.addSubview(selectAllBtn)
        selectAllBtn.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(16)
            make.centerY.equalToSuperview()
            make.height.equalTo(40)
        }
        self.selectAllButton = selectAllBtn
        
        // 添加删除按钮
        let deleteBtn = UIButton(type: .custom)
        deleteBtn.setTitle("删除", for: .normal)
        deleteBtn.setTitleColor(.white, for: .normal)
        deleteBtn.titleLabel?.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        deleteBtn.backgroundColor = UIColor(hex: "#FF8F1F")
        deleteBtn.layer.cornerRadius = 16
        deleteBtn.addTarget(self, action: #selector(deleteSelectedButtonTapped), for: .touchUpInside)
        view.addSubview(deleteBtn)
        deleteBtn.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-16)
            make.centerY.equalToSuperview()
            make.width.equalTo(88)
            make.height.equalTo(32)
        }
        self.deleteButton = deleteBtn
        
        return view
    }()
    
    private var selectAllButton: UIButton?
    private var deleteButton: UIButton?
    
    // MARK: - 数据相关属性
    
    // 草稿视频列表数据
    private var videoItems: [PersonalWorksListDetailData] = []
    private var selectedVideoIDs: Set<String> = []
    
    // 是否处于管理模式
    private var isManageMode: Bool = false {
        didSet {
            manageButton.isSelected = isManageMode
            updateEditModeUI()
            collectionView.reloadData()
            if !isManageMode {
                selectedVideoIDs.removeAll()
                updateSelectAllButtonState()
            }
        }
    }
    
    // 分页相关属性
    private var currentPage: Int = 0
    private var pageSize: Int = 10
    private var hasMoreData: Bool = true
    private var isLoading: Bool = false
    
    // 搜索关键字
    private var searchKeyword: String = ""

    // 标记是否来自编辑页
    var fromEditPage: Bool = false

    // MARK: - 生命周期方法
    
    override func viewDidLoad() {
        super.viewDidLoad()
        navTitle = "草稿箱"

        // 设置导航栏背景色
        navBar.backgroundColor = UIColor(hex: "#F5F5F5")

        // 设置状态栏样式
        setNeedsStatusBarAppearanceUpdate()

        // 如果来自编辑页，重新设置返回按钮的点击事件
        if fromEditPage {
            backButton.removeTarget(self, action: #selector(backButtonTapped), for: .touchUpInside)
            backButton.addTarget(self, action: #selector(customBackButtonTapped), for: .touchUpInside)
        }

        setupUI()
        setupRefreshControls()

        // 初始加载数据
        loadNewData()
    }
    
    override var preferredStatusBarStyle: UIStatusBarStyle {
        return .darkContent // 确保状态栏内容为深色，以适应浅色背景
    }
    
    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        
        // 更新渐变边框的大小
        gradientBorder.frame = searchContainer.bounds
        let shape = gradientBorder.mask as? CAShapeLayer
        shape?.path = UIBezierPath(roundedRect: CGRect(x: 0.5, y: 0.5, width: searchContainer.bounds.width - 1, height: searchContainer.bounds.height - 1), cornerRadius: 20).cgPath
    }
    
    override func viewWillTransition(to size: CGSize, with coordinator: UIViewControllerTransitionCoordinator) {
        super.viewWillTransition(to: size, with: coordinator)
        
        // 在屏幕旋转后重新设置布局
        coordinator.animate(alongsideTransition: { _ in
            // 动画中不处理
        }) { _ in
            // 旋转完成后重新设置布局
            self.collectionView.setCollectionViewLayout(self.createCompositionalLayout(), animated: true)
        }
    }
    
    // MARK: - 私有方法
    
    private func setupUI() {
        // 设置视图背景色
        view.backgroundColor = UIColor(hex: "#F5F5F5")
        contentView.backgroundColor = UIColor(hex: "#F5F5F5")
        
        // 添加右上角管理按钮
        navBar.addSubview(manageButton)
        manageButton.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.right.equalToSuperview().offset(-16)
            make.width.equalTo(40)
            make.height.equalTo(30)
        }
        
        // 添加搜索容器
        contentView.addSubview(searchContainer)
        searchContainer.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(10)
            make.left.equalToSuperview().offset(16)
            make.right.equalToSuperview().offset(-16)
            make.height.equalTo(40)
        }
        
        // 添加渐变边框层
        searchContainer.layer.addSublayer(gradientBorder)
        
        // 添加搜索图标
        searchContainer.addSubview(searchIcon)
        searchIcon.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(12)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(20)
        }
        
        // 添加搜索输入框
        searchContainer.addSubview(searchTextField)
        searchTextField.snp.makeConstraints { make in
            make.left.equalTo(searchIcon.snp.right).offset(8)
            make.right.equalToSuperview().offset(-12)
            make.centerY.equalToSuperview()
            make.height.equalTo(30)
        }
        
        // 添加草稿数量标签
        contentView.addSubview(countLabel)
        countLabel.snp.makeConstraints { make in
            make.top.equalTo(searchContainer.snp.bottom).offset(12)
            make.left.equalToSuperview().offset(16)
            make.height.equalTo(20)
        }
        
        // 添加视频列表
        contentView.addSubview(collectionView)
        collectionView.snp.makeConstraints { make in
            make.top.equalTo(countLabel.snp.bottom).offset(8)
            make.left.right.equalToSuperview()
            make.bottom.equalToSuperview()
        }
        
        // 添加底部操作视图
        view.addSubview(editActionsView)
        editActionsView.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            make.height.equalTo(60)
            make.bottom.equalTo(view.safeAreaLayoutGuide.snp.bottom).offset(60)
        }
    }
    
    // 设置刷新控件
    private func setupRefreshControls() {
        // 设置下拉刷新
        let header = MJRefreshNormalHeader(refreshingTarget: self, refreshingAction: #selector(loadNewData))
        header.setTitle("下拉刷新", for: .idle)
        header.setTitle("松开刷新", for: .pulling)
        header.setTitle("正在刷新", for: .refreshing)
        collectionView.mj_header = header
        
        // 设置上拉加载
        let footer = MJRefreshAutoNormalFooter(refreshingTarget: self, refreshingAction: #selector(loadMoreData))
        footer.setTitle("", for: .idle)
        footer.setTitle("正在加载更多...", for: .refreshing)
        footer.setTitle("没有更多内容了", for: .noMoreData)
        collectionView.mj_footer = footer
    }
    
    // 下拉刷新
    @objc private func loadNewData() {
        currentPage = 0
        hasMoreData = true
        // 重置没有更多数据状态
        collectionView.mj_footer?.resetNoMoreData()
        loadDraftsList(isRefresh: true)
    }
    
    // 上拉加载
    @objc private func loadMoreData() {
        if hasMoreData && !isLoading {
            let nextPage = currentPage + 1
            loadDraftsList(page: nextPage, isRefresh: false)
        } else if !hasMoreData {
            collectionView.mj_footer?.endRefreshingWithNoMoreData()
        } else {
            collectionView.mj_footer?.endRefreshing()
        }
    }
    
    // 加载草稿箱列表
    private func loadDraftsList(page: Int = 0, isRefresh: Bool = false) {
        if isLoading { return }
        isLoading = true
        
        // 如果是下拉刷新，重置页码
        if isRefresh {
            currentPage = 0
            if isManageMode {
                selectedVideoIDs.removeAll()
                updateSelectAllButtonState()
            }
        }
        
        let currentSearchKeyword = self.searchKeyword
        print("开始获取草稿箱列表，页码: \(page), 搜索关键词: \(currentSearchKeyword)")
        
        // 调用获取草稿箱列表API，传入搜索关键词
        APIManager.shared.getVideoWorksDraftsList(page: page, size: pageSize, keywords: currentSearchKeyword.isEmpty ? nil : currentSearchKeyword) { [weak self] result in
            guard let self = self else { return }
            
            self.isLoading = false
            
            // 结束刷新状态
            if isRefresh {
                self.collectionView.mj_header?.endRefreshing()
            } else {
                self.collectionView.mj_footer?.endRefreshing()
            }
            
            switch result {
            case .success(let response):
                if let responseData = response.data {
                    self.handleDraftsListResponse(responseData: responseData, isRefresh: isRefresh)
                } else {
                    print("API响应中没有数据字段或数据为空 (解析后的 response.data 为 nil)")
                    if isRefresh {
                        self.videoItems = []
                        self.updateCountLabel()
                        self.collectionView.reloadData()
                        if self.isManageMode {
                            self.selectedVideoIDs.removeAll()
                            self.updateSelectAllButtonState()
                        }
                    }
                    self.collectionView.mj_footer?.endRefreshingWithNoMoreData()
                }
                
            case .failure(let error):
                print("获取草稿箱列表失败: \(error.localizedDescription)")
                self.showToast("获取草稿箱列表失败，请稍后重试")
            }
        }
    }
    
    // 处理草稿箱列表响应
    private func handleDraftsListResponse(responseData: PersonalWorksListData, isRefresh: Bool) {
        let newItems = responseData.list
        let totalItemsCount = responseData.total
        
        // 如果是刷新，清空现有数据
        if isRefresh {
            videoItems = newItems
        } else {
            // 否则追加数据
            videoItems.append(contentsOf: newItems)
        }
        
        print("获取草稿箱列表成功，当前批次获取到 \(newItems.count) 项，数据源总数变为 \(videoItems.count) 项，API报告总数为 \(totalItemsCount)")
        
        // 更新当前页码，确保在成功处理数据后更新
        if !newItems.isEmpty || isRefresh { 
            self.currentPage = responseData.pageNum 
        }
        
        // 判断是否有更多数据
        hasMoreData = videoItems.count < totalItemsCount
        
        // 更新上拉加载状态
        if hasMoreData {
            collectionView.mj_footer?.resetNoMoreData()
        } else {
            collectionView.mj_footer?.endRefreshingWithNoMoreData()
        }
        
        // 更新草稿数量标签
        updateCountLabel()
        
        // 更新表格视图
        collectionView.reloadData()
        
        // 如果在管理模式，刷新后需要更新全选按钮状态
        if isManageMode {
            updateSelectAllButtonState()
        }
    }
    
    // 更新草稿数量标签
    private func updateCountLabel() {
        countLabel.text = "共\(videoItems.count)个草稿"
    }
    
    // 检查并触发搜索
    private func checkAndTriggerSearch() {
        let newSearchText = searchTextField.text ?? ""
        if newSearchText != searchKeyword {
            searchKeyword = newSearchText
            loadNewData()
        }
    }
    
    private func updateEditModeUI() {
        editActionsView.isHidden = false
        editActionsView.snp.remakeConstraints { make in
            make.left.right.equalToSuperview()
            make.height.equalTo(60)
            if isManageMode {
                make.bottom.equalTo(view.safeAreaLayoutGuide.snp.bottom)
            } else {
                make.bottom.equalTo(view.safeAreaLayoutGuide.snp.bottom).offset(60)
            }
        }
        
        collectionView.snp.remakeConstraints { make in
            make.top.equalTo(countLabel.snp.bottom).offset(8)
            make.left.right.equalToSuperview()
            if isManageMode {
                make.bottom.equalTo(editActionsView.snp.top)
            } else {
                make.bottom.equalToSuperview()
            }
        }
        
        UIView.animate(withDuration: 0.25, animations: {
            self.view.layoutIfNeeded()
        }) { finished in
            if !self.isManageMode {
                self.editActionsView.isHidden = true
            }
        }
    }
    
    private func updateSelectAllButtonState() {
        guard let selectAllButton = self.selectAllButton else { return }
        if videoItems.isEmpty {
            selectAllButton.isSelected = false
            selectAllButton.isEnabled = false
        } else {
            selectAllButton.isEnabled = true
            selectAllButton.isSelected = selectedVideoIDs.count == videoItems.count && !videoItems.isEmpty
        }
        deleteButton?.isEnabled = !selectedVideoIDs.isEmpty
        deleteButton?.alpha = selectedVideoIDs.isEmpty ? 0.5 : 1.0
    }
    
    // MARK: - 事件响应方法
    
    @objc private func manageButtonTapped() {
        isManageMode.toggle()
    }
    
    @objc private func selectAllButtonTapped(_ sender: UIButton) {
        sender.isSelected = !sender.isSelected
        if sender.isSelected {
            selectedVideoIDs.removeAll()
            videoItems.forEach { item in
                selectedVideoIDs.insert(String(item.id))
            }
        } else {
            selectedVideoIDs.removeAll()
        }
        collectionView.reloadData()
        updateSelectAllButtonState()
    }
    
    @objc private func deleteSelectedButtonTapped() {
        guard !selectedVideoIDs.isEmpty else {
            showToast("请先选择要删除的草稿")
            return
        }
        
        // 显示加载提示
        let hud = MBProgressHUD.showAdded(to: view, animated: true)
        hud.label.text = "正在删除..."
        
        // 将选中的ID转换为Int数组
        let idsToDelete = selectedVideoIDs.compactMap { Int($0) }
        
        // 调用删除API
        APIManager.shared.deleteVideoWorksDrafts(ids: idsToDelete) { [weak self] result in
            guard let self = self else { return }
            
            // 隐藏加载提示
            MBProgressHUD.hide(for: self.view, animated: true)
            
            switch result {
            case .success:
                // 删除成功，更新本地数据
                self.videoItems.removeAll { item in
                    return self.selectedVideoIDs.contains(String(item.id))
                }
                
                let deletedCount = self.selectedVideoIDs.count
                self.selectedVideoIDs.removeAll()
                
                // 更新UI
                self.updateCountLabel()
                self.collectionView.reloadData()
                self.isManageMode = false
                
                // 显示成功提示
                self.showToast("已删除 \(deletedCount) 个草稿")
                
            case .failure(let error):
                // 显示错误提示
                self.showToast("删除失败：\(error.localizedDescription)")
            }
        }
    }
    
    // 创建自适应布局
    private func createCompositionalLayout() -> UICollectionViewLayout {
        // 项目定义
        let itemSize = NSCollectionLayoutSize(
            widthDimension: .fractionalWidth(1.0),
            heightDimension: .fractionalHeight(1.0)
        )
        let item = NSCollectionLayoutItem(layoutSize: itemSize)
        
        // 根据设备类型选择每行几个
        var count = 2
        
        if UIDevice.current.userInterfaceIdiom == .pad {
            let screenWidth = UIScreen.main.bounds.width
            if screenWidth > 900 {
                count = 4
            } else if screenWidth > 700 {
                count = 3
            } else {
                count = 2
            }
        } else {
            let screenWidth = UIScreen.main.bounds.width
            if screenWidth < 375 {
                count = 1
            } else if screenWidth < 500 {
                count = 2
            } else {
                count = 3
            }
        }
        
        // 计算每个项目的实际宽度
        let spacing: CGFloat = 10
        let horizontalPadding: CGFloat = 16
        let availableWidth = UIScreen.main.bounds.width - (horizontalPadding * 2) - (CGFloat(count - 1) * spacing)
        let itemWidth = availableWidth / CGFloat(count)
        
        // 使用正确的比例计算高度 - 整个Cell的高度
        let imageHeight = itemWidth * (298.0/179.0)
        let textAreaHeight: CGFloat = 71
        let totalHeight = imageHeight + textAreaHeight
        
        let groupSize = NSCollectionLayoutSize(
            widthDimension: .fractionalWidth(1.0),
            heightDimension: .absolute(totalHeight)
        )
        
        let group = NSCollectionLayoutGroup.horizontal(
            layoutSize: groupSize,
            subitem: item,
            count: count
        )
        group.interItemSpacing = .fixed(spacing)
        
        // 部分定义
        let section = NSCollectionLayoutSection(group: group)
        section.interGroupSpacing = spacing
        section.contentInsets = NSDirectionalEdgeInsets(
            top: spacing, leading: horizontalPadding, bottom: 20, trailing: horizontalPadding
        )
        
        return UICollectionViewCompositionalLayout(section: section)
    }
}

// MARK: - UITextFieldDelegate

extension VideoDraftBoxViewController: UITextFieldDelegate {
    func textFieldShouldReturn(_ textField: UITextField) -> Bool {
        if let keyword = textField.text {
            searchKeyword = keyword
            loadNewData()
        }
        textField.resignFirstResponder()
        return true
    }
    
    func textFieldDidEndEditing(_ textField: UITextField) {
        checkAndTriggerSearch()
    }
}

// MARK: - UICollectionViewDelegate, UICollectionViewDataSource
extension VideoDraftBoxViewController: UICollectionViewDelegate, UICollectionViewDataSource {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return videoItems.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "DraftVideoCollectionCell", for: indexPath) as! DraftVideoCollectionCell
        let item = videoItems[indexPath.item]
        
        var coverImageUrl = item.worksCoverImg
        if !coverImageUrl.isEmpty && !coverImageUrl.hasPrefix("http") {
            coverImageUrl = "https://test-youshu.gzyoushu.com/video" + coverImageUrl
        }
        
        let durationFormatted = formatDuration(seconds: item.duration)
        let timeAgo = formatTimeAgo(timeString: item.createTime)
        
        let isItemSelected = selectedVideoIDs.contains(String(item.id))

        // 处理标题显示逻辑：视频类型如果标题为空则显示"请添加标题"
        var displayTitle = item.worksTitle
        if item.worksType == 1 && (displayTitle.isEmpty || displayTitle == "") {
            displayTitle = "请添加标题"
        }

        // 处理时长显示：笔记类型不显示时长
        let displayDuration = item.worksType == 2 ? "" : durationFormatted

        cell.configure(
            title: displayTitle,
            duration: displayDuration,
            timeAgo: "保存于\(timeAgo)",
            coverImageUrl: coverImageUrl,
            isManageMode: isManageMode,
            isSelectedForManagement: isItemSelected
        )
        
        return cell
    }
    
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        if isManageMode {
            guard indexPath.item < videoItems.count else { return }
            let item = videoItems[indexPath.item]
            let id = String(item.id)
            
            if selectedVideoIDs.contains(id) {
                selectedVideoIDs.remove(id)
            } else {
                selectedVideoIDs.insert(id)
            }
            if let cell = collectionView.cellForItem(at: indexPath) as? DraftVideoCollectionCell {
                let isSelected = selectedVideoIDs.contains(id)
                cell.updateSelectionIndicator(isManageMode: true, isSelectedForManagement: isSelected)
            }
            updateSelectAllButtonState()
        } else {
            print("查看视频: \(indexPath.item)")
            editDraft(at: indexPath.item)
        }
    }
    
    private func formatDuration(seconds: Int) -> String {
        let minutes = seconds / 60
        let remainingSeconds = seconds % 60
        return String(format: "%02d:%02d", minutes, remainingSeconds)
    }
    
    private func formatTimeAgo(timeString: String?) -> String {
        guard let timeString = timeString else { return "未知时间" }
        
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd HH:mm:ss"
        
        if let date = dateFormatter.date(from: timeString) {
            let now = Date()
            let calendar = Calendar.current
            
            let components = calendar.dateComponents([.year, .month, .day, .hour, .minute], from: date, to: now)
            
            if let year = components.year, year > 0 {
                return "\(year)年前"
            }
            if let month = components.month, month > 0 {
                return "\(month)个月前"
            }
            if let day = components.day, day > 0 {
                if day == 1 { return "昨天" }
                return "\(day)天前"
            }
            if let hour = components.hour, hour > 0 {
                return "\(hour)小时前"
            }
            if let minute = components.minute, minute > 0 {
                return "\(minute)分钟前"
            }
            return "刚刚"
        }
        return timeString
    }
    
    private func editDraft(at index: Int) {
        let item = videoItems[index]
        print("编辑草稿: \(item.worksTitle ?? "无标题"), ID: \(item.id)")

        if item.worksType == 2 {
            // 笔记类型，进入笔记编辑页面
            let editVC = NoteEditingDetailsViewController(images: nil)
            editVC.draftsId = item.id // 传入草稿ID
            editVC.isEditMode = true // 设置为编辑模式
            navigationController?.pushViewController(editVC, animated: true)
        } else {
            // 视频类型，进入视频发布页面
            // 构造预填充信息
            let prefill = VideoPrefillInfo(
                worksId: String(item.id), // 使用草稿ID作为worksId
                title: item.worksTitle.isEmpty ? nil : item.worksTitle,
                categoryId: item.worksCategoryId == 0 ? nil : item.worksCategoryId,
                categoryName: item.worksCategoryName,
                privacy: item.privacy,
                allowComment: item.allowComment == 1,
                followComment: item.followComment == 1,
                latitude: Double(item.lat ?? ""),
                longitude: Double(item.lng ?? ""),
                areaCode: nil, // PersonalWorksListDetailData没有areaCode字段
                address: item.address.isEmpty ? nil : item.address,
                scheduleTime: item.extValue
            )

            // 处理封面URL
            var coverImageUrl = item.worksCoverImg
            if !coverImageUrl.isEmpty && !coverImageUrl.hasPrefix("http") {
                coverImageUrl = "https://test-youshu.gzyoushu.com/video" + coverImageUrl
            }

            if !coverImageUrl.isEmpty, let url = URL(string: coverImageUrl) {
                // 异步加载封面图片
                let imageView = UIImageView()
                imageView.kf.setImage(with: url) { [weak self] result in
                    DispatchQueue.main.async {
                        var coverImage: UIImage?
                        switch result {
                        case .success(let value):
                            coverImage = value.image
                        case .failure:
                            coverImage = nil
                        }

                        let vc = VideoEditingDetailsViewController(
                            videoPath: item.worksUrl,
                            coverImage: coverImage,
                            videoDuration: CGFloat(item.duration),
                            videoSize: UInt64(item.size),
                            prefillInfo: prefill
                        )
                        vc.draftsId = item.id // 传入草稿ID
                        vc.isDraftEditMode = true // 设置为草稿编辑模式
                        self?.navigationController?.pushViewController(vc, animated: true)
                    }
                }
            } else {
                // 无封面URL，直接跳转
                let vc = VideoEditingDetailsViewController(
                    videoPath: item.worksUrl,
                    coverImage: nil,
                    videoDuration: CGFloat(item.duration),
                    videoSize: UInt64(item.size),
                    prefillInfo: prefill
                )
                vc.draftsId = item.id // 传入草稿ID
                vc.isDraftEditMode = true // 设置为草稿编辑模式
                navigationController?.pushViewController(vc, animated: true)
            }
        }
    }

    // MARK: - 自定义返回按钮逻辑

    @objc private func customBackButtonTapped() {
        // 如果来自编辑页，返回到编辑页
        if let navigationController = self.navigationController {
            // 查找导航栈中的VideoEditViewController
            for viewController in navigationController.viewControllers.reversed() {
                if viewController is VideoEditViewController {
                    navigationController.popToViewController(viewController, animated: true)
                    return
                }
            }
            // 如果没找到VideoEditViewController，使用默认返回逻辑
            if navigationController.viewControllers.count > 1 {
                navigationController.popViewController(animated: true)
            } else {
                dismiss(animated: true, completion: nil)
            }
        } else {
            dismiss(animated: true, completion: nil)
        }
    }
}

// MARK: - 视频草稿单元格
class DraftVideoCollectionCell: UICollectionViewCell {
    
    // 视频缩略图
    private let thumbnailImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFill
        imageView.clipsToBounds = true
        imageView.layer.cornerRadius = 8
        imageView.backgroundColor = UIColor(hex: "#F0F0F0") // 占位背景色
        return imageView
    }()
    
    // 时长标签背景
    private let durationBgView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.black.withAlphaComponent(0.5)
        view.layer.cornerRadius = 4
        return view
    }()
    
    // 时长标签
    private let durationLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 10)
        label.textColor = .white
        label.textAlignment = .center
        return label
    }()
    
    // 标题标签
    private let titleLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        label.textColor = UIColor(hex: "#333333")
        label.numberOfLines = 2
        return label
    }()
    
    // 时间标签
    private let timeLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 11)
        label.textColor = UIColor(hex: "#999999")
        return label
    }()
    
    // 右上角管理模式选择状态指示器 (新的)
    private let managementSelectionIndicator: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(named: "video_cell_edit_default") // 默认未选中图标
        // imageView.highlightedImage = UIImage(named: "video_cell_edit_selected") // 选中图标 (将通过代码切换)
        imageView.contentMode = .scaleAspectFit
        imageView.isHidden = true // 默认隐藏，只在管理模式下显示
        return imageView
    }()
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        backgroundColor = .white
        layer.cornerRadius = 8
        layer.shadowColor = UIColor.black.withAlphaComponent(0.08).cgColor
        layer.shadowOffset = CGSize(width: 0, height: 2)
        layer.shadowRadius = 4
        layer.shadowOpacity = 1
        layer.masksToBounds = false // 允许阴影超出边界
        
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupUI() {
        // 添加视频缩略图 - 保持比例
        contentView.addSubview(thumbnailImageView)
        thumbnailImageView.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.height.equalTo(thumbnailImageView.snp.width).multipliedBy(298.0/179.0)
        }
        
        // 添加时长背景
        thumbnailImageView.addSubview(durationBgView)
        durationBgView.snp.makeConstraints { make in
            make.right.bottom.equalToSuperview().offset(-8)
            make.height.equalTo(18)
            make.width.greaterThanOrEqualTo(30) // 最小宽度，根据内容自适应
        }
        
        // 添加时长标签
        durationBgView.addSubview(durationLabel)
        durationLabel.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.left.equalToSuperview().offset(4)
            make.right.equalToSuperview().offset(-4)
        }
        
        // 添加标题标签
        contentView.addSubview(titleLabel)
        titleLabel.snp.makeConstraints { make in
            make.top.equalTo(thumbnailImageView.snp.bottom).offset(8)
            make.left.equalToSuperview().offset(8)
            make.right.equalToSuperview().offset(-8)
        }
        
        // 添加时间标签
        contentView.addSubview(timeLabel)
        timeLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(4)
            make.left.equalTo(titleLabel)
            make.right.equalToSuperview().offset(-8)
        }
        
        // 添加新的右上角选择状态指示器
        contentView.addSubview(managementSelectionIndicator)
        managementSelectionIndicator.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(8)
            make.right.equalToSuperview().offset(-8)
            make.width.height.equalTo(24) // 根据你的图标大小调整
        }
        managementSelectionIndicator.isHidden = true // 默认隐藏
    }
    
    private func updateManagementSelectionIndicatorImage(isSelectedForManagement: Bool) {
        managementSelectionIndicator.image = isSelectedForManagement ?
            UIImage(named: "video_cell_edit_selected") :
            UIImage(named: "video_cell_edit_default")
    }

    // 新增：在外部直接更新选中指示器，避免 reloadItems 带来的闪动动画
    func updateSelectionIndicator(isManageMode: Bool, isSelectedForManagement: Bool) {
        managementSelectionIndicator.isHidden = !isManageMode
        if isManageMode {
            updateManagementSelectionIndicatorImage(isSelectedForManagement: isSelectedForManagement)
        }
    }
    
    func configure(title: String, duration: String, timeAgo: String, coverImageUrl: String, isManageMode: Bool, isSelectedForManagement: Bool) {
        titleLabel.text = title
        durationLabel.text = duration
        timeLabel.text = timeAgo

        // 根据duration是否为空来控制时长背景视图的显示
        durationBgView.isHidden = duration.isEmpty
        
        if !coverImageUrl.isEmpty, let url = URL(string: coverImageUrl) {
            thumbnailImageView.kf.indicatorType = .activity
            thumbnailImageView.kf.setImage(
                with: url,
                placeholder: UIImage(named: "default_image_placeholder"),
                options: [
                    .transition(.fade(0.2)),
                    .scaleFactor(UIScreen.main.scale),
                    .cacheOriginalImage
                ]
            )
        } else {
            thumbnailImageView.image = UIImage(named: "default_image_placeholder")
        }
        
        // 根据管理模式更新UI
        managementSelectionIndicator.isHidden = !isManageMode
        if isManageMode {
            updateManagementSelectionIndicatorImage(isSelectedForManagement: isSelectedForManagement)
        }
    }
}

