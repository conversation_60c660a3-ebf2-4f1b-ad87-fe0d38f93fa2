//
//  VideoEditingDetailsViewController.swift
//  Shuxia<PERSON>qi
//
//  Created by yongsheng ye on 2025/3/25.
//
//  视频编辑详情
//
//  ===================== 业务流程与功能清单 =====================
//  入口：编辑视频页（点击"下一步"进入本页）
//
//  1. 封面预览
//     - [x] 初始化显示视频第一帧作为封面
//     - [x] 支持自定义封面编辑与替换
//
//  2. 编辑视频标题
//     - [x] 纯文字输入，支持多行，
//
//  3. 选择分类
//     - [x] 请求分类列表数据（接口对接，当前为模拟）
//     - [x] 记录选中分类id，展示选中分类标题
//
//  4. 添加位置
//     - [x] 获取当前位置（定位权限）
//     - [x] 匹配当前城市id（通过地址json）
//     - [ ] 勾选后上传经纬度
//
//  5. 其他开关项
//     - [x] 允许评论、仅关注可评论、定时发布等，影响上传参数
//
//  6. 返回/编辑视频
//     - [x] 点击"编辑视频"或系统返回，返回上一页
//
//  7. 发布流程
//     - [ ] 请求腾讯云上传签名
//     - [ ] 腾讯云SDK上传视频，回调获取加密视频地址、封面等
//     - [ ] 如有自定义封面，走公司图片上传接口，获取封面url
//     - [ ] 视频地址、封面地址等填入公司服务器接口，完成业务上传
//     - [ ] 4个请求均支持断点重试（如视频已上传，封面失败，重试只上传封面）
//     - [ ] 全部流程完成后弹窗提示成功，自动返回roomVC
//
//  ===================== 开发进度说明 =====================
//  - [x] 已完成：封面预览（首帧+自定义）、标题输入、分类选择、开关项、返回逻辑
//  - [ ] 待开发：位置处理、上传全流程、断点重试、成功提示与跳转
//
//  ======================================================
//
//  请持续同步此注释区，确保每个功能点的开发状态一目了然。

import UIKit
import SnapKit
import HXPhotoPicker
import CoreLocation
import TXLiteAVSDK_UGC

// MARK: - 编辑模式预填充数据模型
/// 当页面以"编辑模式"打开时，可通过 `VideoPrefillInfo` 传入已有的视频信息，
/// 页面将在加载完成后自动填充对应字段。
struct VideoPrefillInfo {
    /// 作品 id（编辑模式必填）
    var worksId: String?
    /// 视频标题
    var title: String?
    /// 分类 id
    var categoryId: Int?
    /// 分类名称（可选，仅用于回显）
    var categoryName: String?
    /// 谁可以看权限：1-公开 2-互相关注的人可见 3-仅自己
    var privacy: Int?
    /// 是否允许评论
    var allowComment: Bool?
    /// 是否仅关注的人可评论
    var followComment: Bool?
    /// 纬度
    var latitude: Double?
    /// 经度
    var longitude: Double?
    /// 行政区划编码
    var areaCode: String?
    /// 位置文字描述
    var address: String?
    /// 定时发布时间，格式 "yyyy-MM-dd HH:mm"
    var scheduleTime: String?

    // MARK: - 草稿相关字段
    /// 作品封面图片URL（草稿编辑时使用）
    var worksCoverImg: String?
    /// 作品URL数组（草稿编辑时使用）
    var worksUrl: String?
    /// 视频ID（草稿编辑时使用）
    var videoId: String?
    /// 作品描述（草稿编辑时使用）
    var worksDescribe: String?
}

class VideoEditingDetailsViewController: BaseViewController, CLLocationManagerDelegate {
    
    // MARK: - UI 组件
    
    // 视频预览容器（由UIView改为UIImageView）
    private lazy var previewImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.backgroundColor = UIColor(hex: "#EEEEEE")
        imageView.layer.cornerRadius = 16
        imageView.clipsToBounds = true
        imageView.contentMode = .scaleAspectFill
        return imageView
    }()
    
    // 编辑视频按钮
    private lazy var editVideoButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setTitle("编辑视频", for: .normal)
        button.setTitleColor(UIColor(hex: "#FF6236"), for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 14)
        button.backgroundColor = .white
        button.layer.cornerRadius = 8
        button.layer.borderWidth = 1
        button.layer.borderColor = UIColor(hex: "#FF6236").cgColor
        button.addTarget(self, action: #selector(editVideoButtonTapped), for: .touchUpInside)
        return button
    }()
    
    // 选取封面按钮
    private lazy var selectCoverButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setTitle("选取封面", for: .normal)
        button.setTitleColor(UIColor(hex: "#333333"), for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 14)
        button.backgroundColor = .white
        button.layer.cornerRadius = 8
        button.layer.borderWidth = 1
        button.layer.borderColor = UIColor(hex: "#CCCCCC").cgColor
        button.addTarget(self, action: #selector(selectCoverButtonTapped), for: .touchUpInside)
        return button
    }()
    
    // 将 titleLabel 重命名为 videoTitleLabel
    private lazy var videoTitleLabel: UILabel = {
        let label = UILabel()
        label.text = "视频标题"
        label.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        label.textColor = UIColor(hex: "#333333")
        return label
    }()
    
    // 2. 修改标题输入框容器
    private lazy var titleContainer: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor(hex: "#EDEDED") // 修改为 #EDEDED
        view.layer.cornerRadius = 8
        return view
    }()
    
    // 修改标题输入框为 UITextView 以支持多行文本
    private lazy var titleTextView: UITextView = {
        let textView = UITextView()
        textView.font = UIFont.systemFont(ofSize: 13) // 修改为13pt字体
        textView.textColor = UIColor(hex: "#333333")
        textView.backgroundColor = .clear // 透明背景
        textView.isScrollEnabled = true // 允许滚动
        textView.textContainerInset = UIEdgeInsets(top: 8, left: 0, bottom: 8, right: 0) // 内边距
        textView.delegate = self // 设置代理
        
        // 设置占位文本
        if textView.text.isEmpty {
            textView.text = "请添加标题"
            textView.textColor = UIColor(hex: "#999999")
        }
        
        return textView
    }()
    
    // 字数限制标签
    private lazy var characterCountLabel: UILabel = {
        let label = UILabel()
        label.text = "0/100"
        label.font = UIFont.systemFont(ofSize: 12)
        label.textColor = UIColor(hex: "#999999")
        label.textAlignment = .right
        return label
    }()
    
    // 设置容器
    private lazy var settingsContainer: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor(hex: "#F5F5F5")
        view.layer.cornerRadius = 8
        return view
    }()
    
    // 选择分类标签
    private lazy var categoryLabel: UILabel = {
        let label = UILabel()
        label.text = "选择分类"
        label.font = UIFont.systemFont(ofSize: 14)
        label.textColor = UIColor(hex: "#333333")
        return label
    }()
    
    // 分割线
    private func createSeparator() -> UIView {
        let separator = UIView()
        separator.backgroundColor = UIColor(hex: "#EEEEEE")
        return separator
    }
    let privacyStatusLabel = UILabel()
    // 添加位置标签
    private lazy var locationLabel: UILabel = {
        let label = UILabel()
        label.text = "添加位置"
        label.font = UIFont.systemFont(ofSize: 14)
        label.textColor = UIColor(hex: "#333333")
        return label
    }()
    
    // 允许评论开关
    private lazy var commentSwitch: UISwitch = {
        let switchControl = UISwitch()
        switchControl.onTintColor = UIColor(hex: "#FF6236")
        switchControl.addTarget(self, action: #selector(commentSwitchChanged(_:)), for: .valueChanged)
        return switchControl
    }()
    
    // 定时发布开关
    private lazy var timedPublishSwitch: UISwitch = {
        let switchControl = UISwitch()
        switchControl.onTintColor = UIColor(hex: "#FF6236")
        switchControl.addTarget(self, action: #selector(timedPublishSwitchChanged(_:)), for: .valueChanged)
        return switchControl
    }()
    
    // 定时发布时间
    private lazy var publishTimeLabel: UILabel = {
        let label = UILabel()
        label.text = "yyyy-mm-dd hh:mm"
        label.font = UIFont.systemFont(ofSize: 14)
        label.textColor = UIColor(hex: "#999999")
        return label
    }()
    
    // 发布按钮
    private lazy var publishButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setTitle("发布", for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        button.layer.cornerRadius = 24  // 修改圆角为高度的一半
        button.addTarget(self, action: #selector(publishButtonTapped), for: .touchUpInside)
        
        // 添加渐变背景
        let gradientLayer = CAGradientLayer()
        gradientLayer.colors = [
            UIColor(hex: "#FF8D36").cgColor,
            UIColor(hex: "#FF5858").cgColor
        ]
        gradientLayer.locations = [0.0, 1.0]
        gradientLayer.startPoint = CGPoint(x: 0, y: 0.5)
        gradientLayer.endPoint = CGPoint(x: 1, y: 0.5)
        gradientLayer.cornerRadius = 24  // 修改圆角为高度的一半
        button.layer.insertSublayer(gradientLayer, at: 0)
        
        // 保存引用以便后续更新
        self.gradientLayer = gradientLayer
        
        return button
    }()
    
    // 保存渐变层引用
    private var gradientLayer: CAGradientLayer?
    
    // 保存当前选中的标签页
    private var selectedTabIndex = 0
    
    // 添加关注人评论开关属性
    private var followersCommentSwitch: UISwitch?
    
    // 1. 添加滚动视图
    private lazy var scrollView: UIScrollView = {
        let scrollView = UIScrollView()
        scrollView.showsVerticalScrollIndicator = true
        scrollView.bounces = true
        return scrollView
    }()
    
    // 2. 添加内容容器视图
    private lazy var scrollContentView: UIView = {
        let view = UIView()
        return view
    }()
    
    // 3. 添加新的属性和方法
    private var locationSwitch: UISwitch?
    private var locationStatusLabel: UILabel?
    
    // MARK: - 新增属性

    private let videoPath: String
    private let coverImage: UIImage?
    private let videoDuration: CGFloat?
    private let videoSize: UInt64?

    // 编辑配置信息，用于埋点
    private var usedEditConfigs: [String: String] = [:]
    
    // 分类标题数组（实际应由接口请求获得）
    private var categoryTitles: [String] = []
    // 选中的分类索引
    private var selectedCategoryIndex: Int?
    // 分类选择器引用
    private var categoryPickerView: UIPickerView?
    private var pickerAlertController: UIAlertController?
    
    // 添加属性：当前隐私选项索引，默认0（公开）
    private var privacySelectedIndex: Int = 0
    
    // ========== 定位相关 ==========
    private var locationManager: CLLocationManager?
    private var locationTimeoutWorkItem: DispatchWorkItem? // 超时任务
    // 缓存Key
    private let kLastLocation = "kLastLocation"
    private let kLastLocationString = "kLastLocationString"
    private let kLastLocationTime = "kLastLocationTime"
    
    // ====== 定位与行政区ID相关属性 ======
    private var currentCoordinate: CLLocationCoordinate2D?
    private var currentLocationString: String?
    private var currentProvince: String?
    private var currentCity: String?
    private var currentCityId: String?
    
    // ====== 省市json缓存 ======
    private var provinceList: [[String: Any]]? // [{label, value, pinyin}]
    private var cityList: [[[String: Any]]]?   // [[{label, value, pinyin}], ...]
    private var cityJsonLoaded = false
    
    private let provinceJsonUrl = "https://image.gzyoushu.com/ae1e13622e464c2899f53858a2596b56.json" // 替换为实际七牛云地址
    private let cityJsonUrl = "https://image.gzyoushu.com/dc571ca6b0cd498d91125ce5a1c02cb7.json" // 替换为实际七牛云地址
    
    // ==========================⬇ 新增属性⬇==========================
    /// 腾讯云 VOD 上传签名，发布流程第一步获取
    private var uploadSignature: String?
    /// 是否有自定义封面
    private var hasCustomCover: Bool = false
    /// 发布器
    private var ugcPublisher: TXUGCPublish?
    /// 发布进度弹窗
    private var publishProgressAlert: UIAlertController?
    /// 进度条
    private var publishProgressView: UIProgressView?
    /// 发布状态标记，防止重复点击
    private var isPublishing = false
    /// 是否正在保存草稿
    private var isSavingDraft = false
    // ==========================⬆ 新增属性⬆==========================
    
    // 在属性区添加分类id数组
    private var categoryIds: [Int] = []
    // 新增：完整详细地址用于上报
    private var currentFullAddress: String?
    
    // ====== 编辑模式相关属性 ======
    /// 预填充信息，非空代表当前为编辑模式
    private var prefillInfo: VideoPrefillInfo?
    /// 预填充的分类 id（用于分类列表获取后回显）
    private var prefillCategoryId: Int?
    
    /// 当前是否为编辑已发布作品而非全新上传
    private var isEditMode: Bool { prefillInfo?.worksId != nil }

    /// 草稿ID，用于草稿编辑模式
    var draftsId: Int = 0

    /// 是否为草稿编辑模式
    var isDraftEditMode: Bool = false
    
    // MARK: - 新增初始化方法
    /// 初始化
    /// - Parameters:
    ///   - videoPath: 本地视频路径
    ///   - coverImage: 默认封面（可选）
    ///   - videoDuration: 时长
    ///   - videoSize: 大小
    ///   - prefillInfo: 若传入则为"编辑模式"，页面会按此结构预填充内容
    ///   - editConfigs: 编辑过程中使用的特效配置信息，用于埋点
    init(videoPath: String,
         coverImage: UIImage?,
         videoDuration: CGFloat?,
         videoSize: UInt64?,
         prefillInfo: VideoPrefillInfo? = nil,
         editConfigs: [String: String]? = nil) {
        self.videoPath = videoPath
        self.coverImage = coverImage
        self.videoDuration = videoDuration
        self.videoSize = videoSize
        self.prefillInfo = prefillInfo
        self.prefillCategoryId = prefillInfo?.categoryId
        self.usedEditConfigs = editConfigs ?? [:]
        super.init(nibName: nil, bundle: nil)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - 生命周期方法
    
    override func viewDidLoad() {
        super.viewDidLoad()

        setupUI()

        // 添加点击背景关闭键盘的手势
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(dismissKeyboard))
        tapGesture.cancelsTouchesInView = false // 不影响其他控件的点击
        view.addGestureRecognizer(tapGesture)

        // 新增：初始化时显示封面
        if let coverImage = coverImage {
            previewImageView.image = coverImage
        }
        // 模拟API请求分类
        fetchCategories()

        // 应用预填充数据（除分类外立即生效）
        applyPrefillInfo()

        // 🔧 新增：检查初始定位权限状态
        checkInitialLocationPermission()
    }
    
    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        
        // 确保渐变层大小与按钮一致
        if let gradientLayer = gradientLayer {
            CATransaction.begin()
            CATransaction.setDisableActions(true)
            gradientLayer.frame = publishButton.bounds
            CATransaction.commit()
        }
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        if let tabBarController = self.tabBarController as? CustomTabBarController {
            tabBarController.hideTabBar(animated: false)
        }
    }
    
    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        // 选取封面或页面出现时，强制隐藏自定义TabBar，防止被遮挡
        if let tabBarController = self.tabBarController as? CustomTabBarController {
            tabBarController.hideTabBar(animated: false)
        }
    }
    
    // MARK: - UI 设置
    
    private func setupUI() {
        // 设置导航栏标题
        navTitle = "发布视频"
        
        // 设置导航栏背景色
        navBar.backgroundColor = UIColor(hex: "#F5F5F5")
        
        // 设置整体背景色
        view.backgroundColor = UIColor(hex: "#F5F5F5")
        contentView.backgroundColor = UIColor(hex: "#F5F5F5")
        
        // 添加滚动视图
        contentView.addSubview(scrollView)
        scrollView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        // 添加滚动内容视图
        scrollView.addSubview(scrollContentView)
        scrollContentView.snp.makeConstraints { make in
            make.top.bottom.equalToSuperview()
            make.left.right.equalTo(contentView)
            make.width.equalTo(contentView)
        }
        
        // 添加视频预览容器 - 修改为固定尺寸140*234并居中
        scrollContentView.addSubview(previewImageView)
        previewImageView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(12) // 距离顶部12pt
            make.centerX.equalToSuperview() // 水平居中
            make.width.equalTo(140) // 固定宽度140
            make.height.equalTo(234) // 固定高度234
        }
        
        // 添加视频播放器视图
        // previewImageView.addSubview(videoPlayerView)
        // videoPlayerView.snp.makeConstraints { make in
        //     make.edges.equalToSuperview()
        // }
        
        // 添加编辑视频按钮
        scrollContentView.addSubview(editVideoButton)
        editVideoButton.snp.makeConstraints { make in
            make.top.equalTo(previewImageView.snp.bottom).offset(12) // 距离封面12pt
            make.right.equalTo(scrollContentView.snp.centerX).offset(-6) // 居左，考虑间距
            make.width.equalTo(96) // 宽度96
            make.height.equalTo(40) // 高度40
        }
        
        // 添加选取封面按钮
        scrollContentView.addSubview(selectCoverButton)
        selectCoverButton.snp.makeConstraints { make in
            make.top.equalTo(previewImageView.snp.bottom).offset(12) // 距离封面12pt
            make.left.equalTo(scrollContentView.snp.centerX).offset(6) // 居右，考虑间距
            make.width.equalTo(96) // 宽度96
            make.height.equalTo(40) // 高度40
        }
        
        // 添加视频标题标签
        scrollContentView.addSubview(videoTitleLabel)
        videoTitleLabel.snp.makeConstraints { make in
            make.top.equalTo(editVideoButton.snp.bottom).offset(24) // 与按钮保持一定距离
            make.left.equalToSuperview().offset(20) // 左边距 20pt
            make.height.equalTo(14)
        }
        
        // 修改标题输入框容器的位置和大小
        scrollContentView.addSubview(titleContainer)
        titleContainer.snp.makeConstraints { make in
            make.top.equalTo(videoTitleLabel.snp.bottom).offset(12) // 与标签保持一定距离
            make.left.equalToSuperview().offset(20) // 左边距 20pt
            make.right.equalToSuperview().offset(-20) // 右边距 20pt
            make.height.equalTo(72) // 高度 72pt
        }
        
        // 添加标题输入框
        titleContainer.addSubview(titleTextView)
        titleTextView.snp.makeConstraints { make in
            make.top.left.equalToSuperview().offset(8)
            make.right.equalToSuperview().offset(-8)
            make.bottom.equalToSuperview().offset(-20) // 留出底部空间给字数标签
        }
        
        // 添加字数限制标签
        titleContainer.addSubview(characterCountLabel)
        characterCountLabel.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-8)
            make.bottom.equalToSuperview().offset(-4)
            make.height.equalTo(16)
            make.width.equalTo(60)
        }
        
        // 调整设置容器的位置
        scrollContentView.addSubview(settingsContainer)
        settingsContainer.snp.makeConstraints { make in
            make.top.equalTo(titleContainer.snp.bottom).offset(16) // 与标题输入框保持一定距离
            make.left.equalToSuperview().offset(16)
            make.right.equalToSuperview().offset(-16)
        }
        
        // 添加设置项
        setupSettingsOptions()
        
        // 添加发布按钮
        scrollContentView.addSubview(publishButton)
        publishButton.snp.makeConstraints { make in
            make.top.equalTo(settingsContainer.snp.bottom).offset(24)
            make.centerX.equalToSuperview()
            make.width.equalTo(168)  // 修改宽度为168
            make.height.equalTo(48)  // 修改高度为48
            make.bottom.equalToSuperview().offset(-24) // 确保底部有足够空间
        }
    }
    
    private func setupSettingsOptions() {
        // 创建垂直堆叠布局
        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.spacing = 0
        stackView.distribution = .fillEqually
        settingsContainer.addSubview(stackView)
        stackView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        // 设置行高
        let rowHeight: CGFloat = 48
        
        // 1. 选择分类
        let categoryView = createSettingRow()
        categoryView.heightAnchor.constraint(equalToConstant: rowHeight).isActive = true
        
        // 修改标签样式
        let categoryTitleLabel = UILabel()
        categoryTitleLabel.text = "选择分类"
        categoryTitleLabel.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        categoryTitleLabel.textColor = UIColor(hex: "#333333")
        
        categoryView.addSubview(categoryTitleLabel)
        categoryTitleLabel.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(20) // 恢复左边距为20
            make.centerY.equalToSuperview()
        }
        
        // 添加选择文字
        let categoryTextLabel = UILabel()
        categoryTextLabel.text = "请选择"
        categoryTextLabel.font = UIFont.systemFont(ofSize: 14)
        categoryTextLabel.textColor = UIColor(hex: "#999999")
        categoryTextLabel.textAlignment = .right
        
        categoryView.addSubview(categoryTextLabel)
        categoryTextLabel.snp.makeConstraints { make in
            make.right.equalTo(categoryView).offset(-36) // 调整右侧间距
            make.centerY.equalToSuperview()
        }
        
        // 添加箭头图标
        let categoryArrowImage = UIImageView(image: UIImage(named: "setting_arrow"))
        categoryView.addSubview(categoryArrowImage)
        categoryArrowImage.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.right.equalToSuperview().offset(-16)
            make.width.height.equalTo(16)
        }
        
        // 添加点击手势
        let categoryTapGesture = UITapGestureRecognizer(target: self, action: #selector(categorySelectorTapped))
        categoryView.addGestureRecognizer(categoryTapGesture)
        categoryView.isUserInteractionEnabled = true
        
        // 2. 添加位置
        let locationView = createSettingRow()
        locationView.heightAnchor.constraint(equalToConstant: rowHeight).isActive = true
        locationView.backgroundColor = UIColor(hex: "#F5F5F5")
        
        // 修改标签文本和样式
        let locationTitleLabel = UILabel()
        locationTitleLabel.text = "添加位置"
        locationTitleLabel.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        locationTitleLabel.textColor = UIColor(hex: "#333333")
        
        locationView.addSubview(locationTitleLabel)
        locationTitleLabel.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(20)
            make.centerY.equalToSuperview()
        }
        
        // 添加开关 - 先创建开关并添加到视图
        let locationSwitch = UISwitch()
        locationSwitch.onTintColor = UIColor(hex: "#FF6236")
        locationSwitch.addTarget(self, action: #selector(locationSwitchChanged(_:)), for: .valueChanged)
        
        locationView.addSubview(locationSwitch)
        locationSwitch.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-16)
            make.centerY.equalToSuperview()
        }
        
        // 保存引用，以便后续访问
        self.locationSwitch = locationSwitch
        
        // 添加"未添加"状态标签 - 现在可以安全引用locationSwitch
        let locationStatusLabel = UILabel()
        locationStatusLabel.text = "未添加"
        locationStatusLabel.font = UIFont.systemFont(ofSize: 14)
        locationStatusLabel.textColor = UIColor(hex: "#999999")
        locationStatusLabel.textAlignment = .right
        
        locationView.addSubview(locationStatusLabel)
        locationStatusLabel.snp.makeConstraints { make in
            make.right.equalTo(locationSwitch.snp.left).offset(-12)
            make.centerY.equalToSuperview()
        }
        
        // 保存状态标签引用
        self.locationStatusLabel = locationStatusLabel
        
        // 3. 谁可以看
        let privacyView = createSettingRow()
        privacyView.heightAnchor.constraint(equalToConstant: rowHeight).isActive = true
        
        let privacyTitleLabel = UILabel()
        privacyTitleLabel.text = "谁可以看"
        privacyTitleLabel.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        privacyTitleLabel.textColor = UIColor(hex: "#333333")
        
        privacyView.addSubview(privacyTitleLabel)
        privacyTitleLabel.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(20) // 恢复左边距为20
            make.centerY.equalToSuperview()
        }
        
        // 添加公开状态和箭头
        privacyStatusLabel.text = "公开"
        privacyStatusLabel.font = UIFont.systemFont(ofSize: 14)
        privacyStatusLabel.textColor = UIColor(hex: "#999999")
        privacyStatusLabel.textAlignment = .right
        
        privacyView.addSubview(privacyStatusLabel)
        privacyStatusLabel.snp.makeConstraints { make in
            make.right.equalTo(privacyView).offset(-36) // 调整右侧距离
            make.centerY.equalToSuperview()
        }
        
        // 添加箭头图标
        let privacyArrowImageView = UIImageView(image: UIImage(named: "setting_arrow"))
        privacyView.addSubview(privacyArrowImageView)
        privacyArrowImageView.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.right.equalToSuperview().offset(-16)
            make.width.height.equalTo(16)
        }
        
        // 添加点击手势
        let privacyTapGesture = UITapGestureRecognizer(target: self, action: #selector(privacySelectorTapped))
        privacyView.addGestureRecognizer(privacyTapGesture)
        privacyView.isUserInteractionEnabled = true
        
        // 4. 允许评论
        let commentsView = createSettingRow()
        commentsView.heightAnchor.constraint(equalToConstant: rowHeight).isActive = true
        
        let commentsLabel = UILabel()
        commentsLabel.text = "允许评论"
        commentsLabel.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        commentsLabel.textColor = UIColor(hex: "#333333")
        
        commentsView.addSubview(commentsLabel)
        commentsLabel.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(20) // 恢复左边距为20
            make.centerY.equalToSuperview()
        }
        
        commentsView.addSubview(commentSwitch)
        commentSwitch.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-16)
            make.centerY.equalToSuperview()
        }
        
        // 5. 仅关注的人可评论
        let followersCommentsView = createSettingRow()
        followersCommentsView.heightAnchor.constraint(equalToConstant: rowHeight).isActive = true
        
        let followersCommentsLabel = UILabel()
        followersCommentsLabel.text = "仅关注的人可评论"
        followersCommentsLabel.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        followersCommentsLabel.textColor = UIColor(hex: "#333333")
        
        followersCommentsView.addSubview(followersCommentsLabel)
        followersCommentsLabel.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(20) // 恢复左边距为20
            make.centerY.equalToSuperview()
        }
        
        // 添加关注评论开关
        let followersCommentSwitch = UISwitch()
        followersCommentSwitch.onTintColor = UIColor(hex: "#FF6236")
        followersCommentSwitch.addTarget(self, action: #selector(followersCommentSwitchChanged(_:)), for: .valueChanged)
        
        followersCommentsView.addSubview(followersCommentSwitch)
        followersCommentSwitch.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-16)
            make.centerY.equalToSuperview()
        }
        
        // 保存引用，以便在评论开关状态变化时更新
        self.followersCommentSwitch = followersCommentSwitch
        
        // 初始状态设置
        followersCommentSwitch.isEnabled = commentSwitch.isOn
        followersCommentSwitch.alpha = commentSwitch.isOn ? 1.0 : 0.5
        
        // 6. 定时发布
        let timedPublishView = createSettingRow()
        timedPublishView.heightAnchor.constraint(equalToConstant: rowHeight).isActive = true
        
        let timedPublishLabel = UILabel()
        timedPublishLabel.text = "定时发布"
        timedPublishLabel.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        timedPublishLabel.textColor = UIColor(hex: "#333333")
        
        timedPublishView.addSubview(timedPublishLabel)
        timedPublishLabel.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(20)
            make.centerY.equalToSuperview()
        }
        
        // 添加发布时间开关
        timedPublishView.addSubview(timedPublishSwitch)
        timedPublishSwitch.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-16)
            make.centerY.equalToSuperview()
        }
        
        // 修改时间标签，添加点击事件
        timedPublishView.addSubview(publishTimeLabel)
        publishTimeLabel.snp.makeConstraints { make in
            make.right.equalTo(timedPublishSwitch.snp.left).offset(-12) // 调整间距为12pt
            make.centerY.equalToSuperview()
        }
        publishTimeLabel.isHidden = true // 初始化时隐藏label，不设置.text
        
        // 确保标签可点击
        publishTimeLabel.isUserInteractionEnabled = true
        let timeGesture = UITapGestureRecognizer(target: self, action: #selector(publishTimeLabelTapped))
        publishTimeLabel.addGestureRecognizer(timeGesture)
        
        // 将所有设置项添加到堆叠视图
        stackView.addArrangedSubview(categoryView)
        stackView.addArrangedSubview(locationView)
        stackView.addArrangedSubview(privacyView)
        stackView.addArrangedSubview(commentsView)
        stackView.addArrangedSubview(followersCommentsView)
        stackView.addArrangedSubview(timedPublishView)

        // 权限UI默认显示"所有人可见"并高亮
        let options = ["所有人可见", "互相关注的人可见", "仅自己可见"]
        privacyStatusLabel.text = options[privacySelectedIndex]
        privacyStatusLabel.textColor = UIColor(hex: "#333333")
    }
    
    private func createSettingRow() -> UIView {
        let view = UIView()
        view.backgroundColor = UIColor(hex: "#F5F5F5")  // 统一背景色为F5F5F5
        
        // 添加底部分割线
        let separator = createSeparator()
        view.addSubview(separator)
        separator.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(1)
            make.right.bottom.equalToSuperview()
            make.height.equalTo(0.5)
        }
        
        return view
    }
    
    // MARK: - 事件响应方法
    
    @objc private func editVideoButtonTapped() {
        print("编辑视频按钮被点击")
        // 先收起键盘
        view.endEditing(true)
        //当前的业务逻辑来看，应该是返回上一页
        if let navigationController = self.navigationController {
            navigationController.popViewController(animated: true)
        } else {
            self.dismiss(animated: true, completion: nil)
        }
    }

    // MARK: - Override Back Button Behaviour
    /// 重写返回按钮事件，弹出拦截弹窗（120*81pt）
    @objc override func backButtonTapped() {
        // 先收起键盘
        view.endEditing(true)

        // 如果正在发布中则不允许返回
        guard !isPublishing else { return }

        // 如果是草稿编辑模式，直接返回不弹出拦截弹窗
        if isDraftEditMode {
            if let nav = self.navigationController, nav.viewControllers.count > 1 {
                nav.popViewController(animated: true)
            } else {
                self.dismiss(animated: true, completion: nil)
            }
            return
        }

        // 1. 创建弹窗
        let popup = BackInterceptPopupView(frame: .zero)
        // 2. 处理用户选择
        popup.onActionSelected = { [weak self] action in
            guard let self = self else { return }
            switch action {
            case .discard:
                // 不保存直接返回
                if let nav = self.navigationController, nav.viewControllers.count > 1 {
                    nav.popViewController(animated: true)
                } else {
                    self.dismiss(animated: true, completion: nil)
                }
            case .saveDraft:
                self.saveDraftAndExit()
            }
        }
        // 3. 显示在返回按钮下方，横向偏移4pt
        popup.present(below: self.backButton)
    }

    /// 保存草稿并退出：走完整上传流程，但最终调用草稿接口
    private func saveDraftAndExit() {
        // 若正在上传/保存，则忽略重复请求
        guard !isPublishing else { return }
        isSavingDraft = true
        // 复用获取签名→上传→回调流程
        getTXSign()
    }

    @objc private func selectCoverButtonTapped() {
        print("选取封面按钮被点击")
        // 先收起键盘
        view.endEditing(true)
        // 处理选取封面逻辑
        selectPhotoButtonTapped()
    }
    
    @objc private func categorySelectorTapped() {
        // 先收起键盘
        view.endEditing(true)

        guard !categoryTitles.isEmpty else {
            let alert = UIAlertController(title: "提示", message: "暂无可选分类，请稍后重试", preferredStyle: .alert)
            alert.addAction(UIAlertAction(title: "确定", style: .default))
            present(alert, animated: true)
            return
        }
        // 使用自定义分类选择弹窗
        let popup = CategoryPickerPopupView(categories: categoryTitles, selectedIndex: selectedCategoryIndex)
        popup.onCancel = { }
        popup.onConfirm = { [weak self] idx in
            self?.selectedCategoryIndex = idx
            if let categoryTextLabel = self?.findCategoryTextLabel() {
                categoryTextLabel.text = self?.categoryTitles[idx]
                categoryTextLabel.textColor = (idx == 0) ? UIColor(hex: "#999999") : UIColor(hex: "#333333")
            }
            // 这里可以获取到选中的分类id和typeName
            let selectedId = self?.categoryIds.indices.contains(idx) == true ? self?.categoryIds[idx] : nil
            let selectedName = self?.categoryTitles.indices.contains(idx) == true ? self?.categoryTitles[idx] : nil
            print("选中分类id: \(selectedId ?? -1), 名称: \(selectedName ?? "")")
            // 可在此处将id同步到上传参数等业务逻辑
        }
        UIApplication.shared.keyWindow?.addSubview(popup)
    }
    
    @objc private func locationSelectorTapped() {
        print("选择位置按钮被点击")
        // 弹出位置选择器
    }
    
    @objc private func commentSwitchChanged(_ sender: UISwitch) {
        print("允许评论开关状态: \(sender.isOn)")
        // 先收起键盘
        view.endEditing(true)

        // 更新关注人评论开关的可用状态
        followersCommentSwitch?.isEnabled = sender.isOn
        
        // 使用动画改变透明度，提供更好的视觉反馈
        UIView.animate(withDuration: 0.2) {
            self.followersCommentSwitch?.alpha = sender.isOn ? 1.0 : 0.5
        }
        
        // 如果关闭评论，同时关闭关注人评论
        if !sender.isOn {
            followersCommentSwitch?.setOn(false, animated: true)
        }
    }
    
    @objc private func privacySelectorTapped() {
        // 先收起键盘
        view.endEditing(true)

        // 弹出可见性选择器，回调中联动UI和数据
        let selector = VisiblePopSelector(selectedIndex: privacySelectedIndex) { [weak self] selectedIdx in
            guard let self = self else { return }
            self.privacySelectedIndex = selectedIdx
            // 更新UI
            let options = [
                "所有人可见",
                "互相关注的人可见",
                "仅自己可见"
            ]
            self.privacyStatusLabel.text = options[selectedIdx]
            self.privacyStatusLabel.textColor = UIColor(hex: "#333333")
            // 如有需要，可同步到上传参数
        }
        selector.show(in: self.view)
    }
    
    @objc private func timedPublishSwitchChanged(_ sender: UISwitch) {
        print("定时发布开关状态: \(sender.isOn)")
        // 先收起键盘
        view.endEditing(true)

        // 显示或隐藏时间标签
        publishTimeLabel.isHidden = !sender.isOn
        
        // 如果打开，显示时间选择器
        if sender.isOn {
            showDateTimePicker()
        }
    }
    
    @objc private func publishButtonTapped() {
        // 先收起键盘
        view.endEditing(true)

        // 1. 校验必填项
        guard validateInputs() else { return }

        if isEditMode && !isDraftEditMode {
            // 编辑已发布作品模式：仅修改信息，不上传视频
            updateVideoInfoOnly()
        } else if isDraftEditMode && draftsId > 0 {
            // 草稿编辑发布模式：直接发布，使用草稿的原始数据
            publishDraftDirectly()
        } else {
            // 新发布：走上传流程
            getTXSign()
        }
    }

    private func validateInputs() -> Bool {
        // 标题
        let title = titleTextView.text.trimmingCharacters(in: .whitespacesAndNewlines)
        if title.isEmpty || title == "请添加标题" {
            showAlert(title: "提示", message: "请填写视频标题")
            return false
        }
        // 分类
        let idx = selectedCategoryIndex ?? 0
        if !categoryIds.indices.contains(idx) || categoryIds[idx] == -1 {
            showAlert(title: "提示", message: "请选择视频分类")
            return false
        }
        // 谁可以看（权限类型）
        if privacySelectedIndex < 0 || privacySelectedIndex > 2 {
            showAlert(title: "提示", message: "请选择谁可以看权限")
            return false
        }
        return true
    }

    private func showAlert(title: String, message: String) {
        let alert = UIAlertController(title: title, message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }

    /// 草稿直接发布（不需要重新上传视频）
    private func publishDraftDirectly() {
        guard let prefill = prefillInfo else {
            showAlert(title: "错误", message: "草稿数据缺失")
            return
        }

        // 1. 禁用按钮，显示发布中状态
        publishButton.isEnabled = false
        publishButton.setTitle("发布中", for: .normal)

        // 2. 构建发布参数
        var params: [String: Any] = [:]

        // 标题
        let title = titleTextView.text.trimmingCharacters(in: .whitespacesAndNewlines)
        params["worksTitle"] = (title == "请添加标题") ? "" : title

        // 分类：草稿编辑模式使用草稿ID作为worksCategoryId
        params["worksCategoryId"] = draftsId

        // 权限
        params["privacy"] = privacySelectedIndex + 1

        // 评论
        params["allowComment"] = commentSwitch.isOn ? 1 : 0
        params["followComment"] = followersCommentSwitch?.isOn == true ? 1 : 0

        // 经纬度、地址
        if let lat = currentCoordinate?.latitude { params["lat"] = String(lat) }
        if let lng = currentCoordinate?.longitude { params["lng"] = String(lng) }
        if let areaCode = currentCityId { params["areaCode"] = areaCode }
        if let address = currentFullAddress { params["address"] = address }

        // 定时发布
        if timedPublishSwitch.isOn, let text = publishTimeLabel.text, !text.isEmpty, text != "yyyy-mm-dd hh:mm" {
            params["extValue"] = text
        }

        // 视频相关：使用草稿的原始数据
        params["videoId"] = prefill.videoId ?? ""

        // 解析worksUrl JSON字符串
        if let worksUrlString = prefill.worksUrl, !worksUrlString.isEmpty {
            if let data = worksUrlString.data(using: .utf8),
               let urlArray = try? JSONSerialization.jsonObject(with: data, options: []) as? [String] {
                params["worksUrl"] = urlArray
            } else {
                params["worksUrl"] = [worksUrlString] // 如果解析失败，直接作为单个URL
            }
        } else {
            params["worksUrl"] = []
        }

        // 封面：如果用户更换了封面需要重新上传，否则使用草稿原始封面
        params["worksCoverImg"] = prefill.worksCoverImg ?? ""

        params["duration"] = Int(videoDuration ?? 0)
        params["size"] = Int(videoSize ?? 0)
        params["worksType"] = 1 // 1-视频
        params["draftsId"] = draftsId // 标识这是草稿发布

        // 作品描述
        if let describe = prefill.worksDescribe, !describe.isEmpty {
            params["worksDescribe"] = describe
        }

        // 添加编辑配置埋点信息
        if !usedEditConfigs.isEmpty {
            params["useEditConfigId"] = usedEditConfigs
        }

        print("[草稿发布] 最终参数: \(params)")

        // 3. 调用发布接口
        APIManager.shared.uploadShortVideoWorks(params: params) { [weak self] result in
            DispatchQueue.main.async {
                guard let self = self else { return }
                self.publishButton.isEnabled = true
                self.publishButton.setTitle("发布", for: .normal)

                switch result {
                case .success(let response):
                    if response.isSuccess {
                        // 发布成功，删除草稿
                        self.deleteDraftAfterPublish()
                    } else {
                        self.showAlert(title: "发布失败", message: response.displayMessage)
                    }
                case .failure(let error):
                    self.showAlert(title: "发布失败", message: error.errorMessage)
                }
            }
        }
    }
                        
    //获取腾讯签名
    func getTXSign() {
        // 1️⃣ 禁用按钮，显示发布中状态
        publishButton.isEnabled = false
        publishButton.setTitle("发布中", for: .normal)

        // 2️⃣ 调用 API 获取腾讯云上传签名
        APIManager.shared.getVodSignature { [weak self] result in
            guard let self = self else { return }
            DispatchQueue.main.async {
                switch result {
                case .success(let response):
                    if response.status == 200, let signData = response.data {
                        // 保存签名
                        self.uploadSignature = signData.sign
                        print("成功获取上传签名: \(signData.sign)")
                        // 获取签名成功，开始上传视频到腾讯云
                        self.startVideoUpload()
                    } else {
                        // 失败处理
                        let message = response.errMsg.isEmpty ? "获取上传签名失败" : response.errMsg
                        self.publishButton.isEnabled = true
                        self.publishButton.setTitle("发布", for: .normal)
                        self.showAlert(title: "发布失败", message: message)
                    }
                case .failure(let error):
                    self.publishButton.isEnabled = true
                    self.publishButton.setTitle("发布", for: .normal)
                    self.showAlert(title: "发布失败", message: error.errorMessage)
                }
            }
        }
    }
    
    // 显示日期时间选择器
    private func showDateTimePicker() {
        // 先收起键盘
        view.endEditing(true)

        // 创建日期选择器
        let datePicker = UIDatePicker()
        datePicker.datePickerMode = .dateAndTime
        datePicker.preferredDatePickerStyle = .wheels
        
        // 设置最小日期为当前时间
        datePicker.minimumDate = Date()
        
        // 创建警告控制器
        let alertController = UIAlertController(title: "\n\n\n\n\n\n\n\n\n\n\n", message: nil, preferredStyle: .actionSheet)
        
        // 将日期选择器添加到警告控制器
        alertController.view.addSubview(datePicker)
        datePicker.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalToSuperview().offset(8)
            make.left.equalToSuperview().offset(8)
            make.right.equalToSuperview().offset(-8)
        }
        
        // 弹窗出现时，label显示当前时间
        let now = Date()
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd HH:mm"
        self.publishTimeLabel.text = formatter.string(from: now)
        self.publishTimeLabel.isHidden = false
        
        // 添加确定和取消按钮
        let doneAction = UIAlertAction(title: "确定", style: .default) { [weak self] _ in
            // 更新日期标签
            let formatter = DateFormatter()
            formatter.dateFormat = "yyyy-MM-dd HH:mm"
            self?.publishTimeLabel.text = formatter.string(from: datePicker.date)
            self?.publishTimeLabel.isHidden = false // 确保显示
        }
        
        let cancelAction = UIAlertAction(title: "取消", style: .cancel) { [weak self] _ in
            // 关闭定时发布开关
            self?.timedPublishSwitch.setOn(false, animated: true)
            // 隐藏时间label
            self?.publishTimeLabel.isHidden = true
        }
        
        alertController.addAction(doneAction)
        alertController.addAction(cancelAction)
        
        // 显示警告控制器
        present(alertController, animated: true, completion: nil)
    }
    
    @objc private func dismissKeyboard() {
        view.endEditing(true)
    }
    
    @objc private func locationSwitchChanged(_ sender: UISwitch) {
        print("添加位置开关状态: \(sender.isOn)")
        // 先收起键盘
        view.endEditing(true)

        if sender.isOn {
            // 🔧 检查定位权限状态
            let status = CLLocationManager.authorizationStatus()

            switch status {
            case .denied, .restricted:
                // 权限被拒绝，强制关闭开关并提示用户
                sender.setOn(false, animated: true)
                showLocationPermissionAlert()
                return
            case .notDetermined:
                // 权限未确定，先请求权限
                if locationManager == nil {
                    locationManager = CLLocationManager()
                    locationManager?.delegate = self
                }
                locationManager?.requestWhenInUseAuthorization()
                return
            case .authorizedWhenInUse, .authorizedAlways:
                // 权限已授权，继续执行定位逻辑
                break
            @unknown default:
                sender.setOn(false, animated: true)
                return
            }

            // 优先使用系统缓存定位：可立即获得地址并存储 cityId，提升响应速度
            if locationManager == nil {
                locationManager = CLLocationManager()
                locationManager?.delegate = self
            }

            if let cachedLocation = locationManager?.location {
                // ⏱️ 立即解析缓存定位，快速给出结果
                reverseGeocodeAndShow(location: cachedLocation)
            } else {
                // 没有缓存则先提示定位中
                locationStatusLabel?.text = "定位中..."
            }

            // ⚡ 再发起一次新定位，获取更精确的当前位置，回来后会覆盖同一组变量
            startLocationQuick()
        } else {
            // 🔧 修复：关闭定位时停止定位服务并清空显示
            locationManager?.stopUpdatingLocation()
            locationStatusLabel?.text = "未添加"
        }
    }
    
    private func startLocationQuick() {
        if locationManager == nil {
            locationManager = CLLocationManager()
            locationManager?.delegate = self
        }
        let status = CLLocationManager.authorizationStatus()
        if status == .notDetermined {
            locationManager?.requestWhenInUseAuthorization()
        } else if status == .authorizedWhenInUse || status == .authorizedAlways {
            locationManager?.requestLocation()
        } else {
            let alert = UIAlertController(title: "无法获取定位", message: "请在设置中开启定位权限", preferredStyle: .alert)
            alert.addAction(UIAlertAction(title: "确定", style: .default))
            present(alert, animated: true)
        }
    }

    // 逆地理编码并显示
    private func reverseGeocodeAndShow(location: CLLocation) {
        let geocoder = CLGeocoder()
        geocoder.reverseGeocodeLocation(location) { [weak self] placemarks, error in
            guard let self = self else { return }

            // 🔧 修复：检查位置开关状态，如果已关闭则不更新UI
            guard self.locationSwitch?.isOn == true else {
                print("位置开关已关闭，忽略逆地理编码结果")
                return
            }

            if let error = error {
                print("逆地理编码失败: \(error)")
                self.locationStatusLabel?.text = "未添加"
                self.currentCoordinate = nil
                self.currentLocationString = nil
                self.currentProvince = nil
                self.currentCity = nil
                self.currentCityId = nil
                self.currentFullAddress = nil
                return
            }
            guard let placemark = placemarks?.first else {
                self.locationStatusLabel?.text = "未添加"
                self.currentCoordinate = nil
                self.currentLocationString = nil
                self.currentProvince = nil
                self.currentCity = nil
                self.currentCityId = nil
                self.currentFullAddress = nil
                return
            }
            let province = placemark.administrativeArea ?? ""
            let city = placemark.locality ?? placemark.subAdministrativeArea ?? ""
            // UI只显示省+市
            let display = "\(province) \(city)"
            self.locationStatusLabel?.text = display
            // 保存经纬度和定位地址
            self.currentCoordinate = location.coordinate
            self.currentProvince = province
            self.currentCity = city
            // 完整详细地址（省、市、区、街道、门牌号、name等）
            let district = placemark.subLocality ?? ""
            let thoroughfare = placemark.thoroughfare ?? ""
            let subThoroughfare = placemark.subThoroughfare ?? ""
            let name = placemark.name ?? ""
            let detail = [province, city, district, thoroughfare, subThoroughfare, name]
                .filter { !$0.isEmpty }
                .joined()
            self.currentFullAddress = detail.isEmpty ? display : detail
            self.currentLocationString = display // 保持兼容
            // 打印经纬度
            print("经纬度: \(location.coordinate.latitude), \(location.coordinate.longitude)")
            // 查询市区id
            self.fetchProvinceAndCityJsonIfNeeded { [weak self] in
                self?.matchCityIdByProvinceAndCity()
            }
        }
    }

    // ====== 省市json加载与匹配 ======
    private func fetchProvinceAndCityJsonIfNeeded(completion: @escaping () -> Void) {
        if cityJsonLoaded {
            completion()
            return
        }
        let group = DispatchGroup()
        group.enter()
        URLSession.shared.dataTask(with: URL(string: provinceJsonUrl)!) { data, _, _ in
            if let data = data,
               let arr = try? JSONSerialization.jsonObject(with: data) as? [[String: Any]] {
                self.provinceList = arr
            }
            group.leave()
        }.resume()
        group.enter()
        URLSession.shared.dataTask(with: URL(string: cityJsonUrl)!) { data, _, _ in
            if let data = data,
               let arr = try? JSONSerialization.jsonObject(with: data) as? [[[String: Any]]] {
                self.cityList = arr
            }
            group.leave()
        }.resume()
        group.notify(queue: .main) {
            self.cityJsonLoaded = true
            completion()
        }
    }

    private func matchCityIdByProvinceAndCity() {
        guard let province = currentProvince, let city = currentCity, let provinceList = provinceList, let cityList = cityList else {
            print("省市信息或json未加载")
            return
        }
        // 1. 先查省在省表中的index
        guard let provinceIndex = provinceList.firstIndex(where: { ($0["label"] as? String)?.contains(province) == true }) else {
            print("未找到省：\(province)")
            return
        }
        // 2. 再查市在对应省的市表中的id
        let cities = cityList[provinceIndex]
        if let cityObj = cities.first(where: { ($0["label"] as? String)?.contains(city) == true }) {
            let cityId = cityObj["value"] as? String
            currentCityId = cityId
            print("匹配到市区id: \(cityId ?? "")")
        } else {
            print("未找到市：\(city)")
            currentCityId = nil
        }
    }

    // CLLocationManagerDelegate
    func locationManager(_ manager: CLLocationManager, didUpdateLocations locations: [CLLocation]) {
        guard let location = locations.first else { return }
        reverseGeocodeAndShow(location: location)
    }

    func locationManager(_ manager: CLLocationManager, didFailWithError error: Error) {
        print("定位失败: \(error.localizedDescription)")
        locationStatusLabel?.text = "未添加"
        let alert = UIAlertController(title: "定位失败", message: "请检查定位服务或网络", preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }

    // iOS 14+ 权限变化回调
    func locationManager(_ manager: CLLocationManager, didChangeAuthorization status: CLAuthorizationStatus) {
        print("定位权限状态变化: \(status.rawValue)")

        switch status {
        case .authorizedWhenInUse, .authorizedAlways:
            // 用户授权了定位权限，开始定位
            print("用户授权了定位权限，开始定位")
            if locationSwitch?.isOn == true {
                locationManager?.requestLocation()
            }
        case .denied, .restricted:
            // 用户拒绝了定位权限，强制关闭开关
            print("用户拒绝了定位权限")
            locationStatusLabel?.text = "未添加"
            locationSwitch?.setOn(false, animated: true)
            showLocationPermissionAlert()
        case .notDetermined:
            print("等待用户授权决定")
        @unknown default:
            print("未知的定位权限状态")
            locationSwitch?.setOn(false, animated: true)
        }
    }

    // 🔧 新增：显示定位权限提示弹窗
    private func showLocationPermissionAlert() {
        let alert = UIAlertController(
            title: "需要定位权限",
            message: "请在设置中开启定位权限，以便添加位置信息",
            preferredStyle: .alert
        )

        alert.addAction(UIAlertAction(title: "取消", style: .cancel))
        alert.addAction(UIAlertAction(title: "去设置", style: .default) { _ in
            if let settingsUrl = URL(string: UIApplication.openSettingsURLString) {
                UIApplication.shared.open(settingsUrl)
            }
        })

        present(alert, animated: true)
    }

    // 🔧 新增：检查初始定位权限状态
    private func checkInitialLocationPermission() {
        let status = CLLocationManager.authorizationStatus()

        switch status {
        case .denied, .restricted:
            // 权限被拒绝，确保开关处于关闭状态
            locationSwitch?.setOn(false, animated: false)
            locationStatusLabel?.text = "未添加"
        case .authorizedWhenInUse, .authorizedAlways:
            // 权限已授权，开关状态保持不变
            break
        case .notDetermined:
            // 权限未确定，开关状态保持不变
            break
        @unknown default:
            // 未知状态，确保开关处于关闭状态
            locationSwitch?.setOn(false, animated: false)
            locationStatusLabel?.text = "未添加"
        }
    }
    
    @objc private func followersCommentSwitchChanged(_ sender: UISwitch) {
        print("仅关注的人可评论开关状态: \(sender.isOn)")
        // 先收起键盘
        view.endEditing(true)
        // 这里可以添加其他业务逻辑
    }
    
    @objc private func publishTimeLabelTapped() {
        print("时间标签被点击")
        // 先收起键盘
        view.endEditing(true)
        showDateTimePicker()
    }
    
    // MARK: - HXPhotoPicker 选择与裁剪
    @objc private func selectPhotoButtonTapped() {
        var config = PickerConfiguration()
        config.selectMode = .single
        config.selectOptions = .photo
        config.photoSelectionTapAction = .openEditor
        config.modalPresentationStyle = .fullScreen // 选择器全屏
        config.editor.modalPresentationStyle = .fullScreen // 编辑器全屏
        config.editor.toolsView.toolOptions = [
            EditorConfiguration.ToolsView.Options(
                imageType: .local("hx_editor_photo_crop"),
                type: .cropSize
            )
        ]
        config.editor.photo.defaultSelectedToolOption = .cropSize
        config.editor.isWhetherFinishButtonDisabledInUneditedState = true
        config.editor.cropSize.aspectRatios = [
            .init(title: .custom("9:16"), ratio: CGSize(width: 9, height: 16))
        ]
        config.editor.cropSize.aspectRatio = CGSize(width: 9, height: 16)
        config.editor.cropSize.isFixedRatio = true
        config.editor.cropSize.defaultSeletedIndex = 0

        Task { [weak self] in
            guard let self = self else { return }
            do {
                let images: [UIImage] = try await PhotoPickerController.picker(config, fromVC: self)
                if let image = images.first {
//                    print("[DEBUG] 选择的图片: \(image)")
                    self.previewImageView.image = image
                    self.hasCustomCover = true // 标记为自定义封面
                    // 选取封面后，强制隐藏TabBar，防止遮挡
                    if let tabBarController = self.tabBarController as? CustomTabBarController {
                        tabBarController.hideTabBar(animated: false)
                    }
                }
            } catch {
                print("[DEBUG] 用户取消或出错: \(error)")
            }
        }
    }
    
    // MARK: - 分类API模拟
    private func fetchCategories() {
        // 调用真实API获取分类列表
        APIManager.shared.getVideoTypeList(isHomeShow: false) { [weak self] result in
            DispatchQueue.main.async {
                switch result {
                case .success(let response):
                    guard response.isSuccess, let data = response.data else {
                        self?.categoryTitles = ["请选择"]
                        self?.categoryIds = [-1]
                        self?.selectedCategoryIndex = 0
                        if let categoryTextLabel = self?.findCategoryTextLabel() {
                            categoryTextLabel.text = "请选择"
                            categoryTextLabel.textColor = UIColor(hex: "#999999")
                        }
                        return
                    }
                    var titles = data.compactMap { $0.typeName }
                    var ids = data.compactMap { $0.id }
                    titles.insert("请选择", at: 0)
                    ids.insert(-1, at: 0)
                    self?.categoryTitles = titles
                    self?.categoryIds = ids
                    self?.selectedCategoryIndex = 0
                    if let categoryTextLabel = self?.findCategoryTextLabel() {
                        categoryTextLabel.text = "请选择"
                        categoryTextLabel.textColor = UIColor(hex: "#999999")
                    }
                    #if DEBUG
                    print("[分类API] 收到列表 count=\(titles.count)")
                    #endif
                    // 👉 回显编辑模式下的已选分类
                    self?.applyPrefillCategoryIfNeeded()
                case .failure(_):
                    self?.categoryTitles = ["请选择"]
                    self?.categoryIds = [-1]
                    self?.selectedCategoryIndex = 0
                    if let categoryTextLabel = self?.findCategoryTextLabel() {
                        categoryTextLabel.text = "请选择"
                        categoryTextLabel.textColor = UIColor(hex: "#999999")
                    }
                }
            }
        }
    }
    
    // 辅助方法：找到分类TextLabel（可根据你的UI结构调整）
    private func findCategoryTextLabel() -> UILabel? {
        // 遍历settingsContainer的subviews找到categoryTextLabel
        for subview in settingsContainer.subviews {
            if let stack = subview as? UIStackView {
                for row in stack.arrangedSubviews {
                    for label in row.subviews where label is UILabel {
                        let lbl = label as! UILabel
                        if lbl.text == "请选择" || categoryTitles.contains(lbl.text ?? "") {
                            return lbl
                        }
                    }
                }
            }
        }
        return nil
    }
    
    // MARK: - 私有工具
    
    /// 将当前封面图片保存至临时目录（如果有的话），返回保存路径
    private func saveCoverToTempIfNeeded() -> String? {
        guard let image = previewImageView.image else { return nil }
        let tempDir = URL(fileURLWithPath: NSTemporaryDirectory(), isDirectory: true)
        let fileURL = tempDir.appendingPathComponent("cover_\(Int(Date().timeIntervalSince1970)).jpg")
        if let data = image.jpegData(compressionQuality: 0.85) {
            do {
                try data.write(to: fileURL)
                return fileURL.path
            } catch {
                print("[发布] 保存封面失败: \(error)")
            }
        }
        return nil
    }
    
    /// 开始调用 TXUGCPublish 上传视频
    private func startVideoUpload() {
        guard !isPublishing else { return }
        guard let signature = uploadSignature else {
//            showAlert(title: "错误", message: "缺少上传签名")
            return
        }
        // 构造发布参数
        let param = TXPublishParam()
        param.signature = signature
        param.videoPath = videoPath
        if hasCustomCover, let coverPath = saveCoverToTempIfNeeded() {
            param.coverPath = coverPath
        } else if let defaultCover = coverImage {
            // 没有自定义封面，使用编辑页传入的默认封面图片
            let tempDir = URL(fileURLWithPath: NSTemporaryDirectory(), isDirectory: true)
            let fileURL = tempDir.appendingPathComponent("default_cover_\(Int(Date().timeIntervalSince1970)).jpg")
            if let data = defaultCover.jpegData(compressionQuality: 0.85) {
                do {
                    try data.write(to: fileURL)
                    param.coverPath = fileURL.path
                } catch {
                    print("[发布] 保存默认封面失败: \(error)")
                }
            }
        }
        param.enableResume = true
        param.enableHTTPS = true

        // 创建发布器并设置代理
        let publisher = TXUGCPublish()
        publisher.delegate = self
        ugcPublisher = publisher

        // 显示简化的发布中HUD
        let alert = UIAlertController(title: "发布中", message: "正在发布视频...", preferredStyle: .alert)
        present(alert, animated: true)

        publishProgressAlert = alert
        publishProgressView = nil // 不显示进度条
        isPublishing = true

        // 调用发布
        let ret = publisher.publishVideo(param) ?? -1
        if ret != 0 {
            // 调用失败，立即反馈
            dismiss(animated: true) { [weak self] in
//                self?.showAlert(title: "发布失败", message: "启动上传失败，错误码: \(ret)")
            }
            isPublishing = false
        }
    }

    // ===================== 草稿删除工具 =====================
    /// 草稿发布成功后删除草稿
    private func deleteDraftAfterPublish() {
        APIManager.shared.deleteVideoWorksDrafts(ids: [self.draftsId]) { [weak self] result in
            DispatchQueue.main.async {
                guard let self = self else { return }
                switch result {
                case .success(_):
                    print("草稿删除成功，ID: \(self.draftsId)")
                    let alert = UIAlertController(title: "发布成功", message: "视频发布成功！", preferredStyle: .alert)
                    alert.addAction(UIAlertAction(title: "确定", style: .default) { _ in
                        self.navigateBackToPublishEntrance()
                    })
                    self.present(alert, animated: true)
                case .failure(let error):
                    print("草稿删除失败，ID: \(self.draftsId), 错误: \(error)")
                    // 即使删除草稿失败，也显示发布成功的提示
                    let alert = UIAlertController(title: "发布成功", message: "视频发布成功！", preferredStyle: .alert)
                    alert.addAction(UIAlertAction(title: "确定", style: .default) { _ in
                        self.navigateBackToPublishEntrance()
                    })
                    self.present(alert, animated: true)
                }
            }
        }
    }

    // ===================== 统一导航返回工具 =====================
    /// 发布成功后一次性返回录制入口页面（CreativeCenter / CustomTabBar）
    fileprivate func navigateBackToPublishEntrance() {
        // 优先检查当前VC所在的 navigationController
        if let nav = self.navigationController {
            // Case A: nav 是被 modal present 出来的（TabBar 录制入口）
            if nav.presentingViewController != nil {
                nav.dismiss(animated: true)
                return
            }
            // Case B: nav 栈内存在创作中心等入口，直接 popTo
            if let targetVC = nav.viewControllers.first(where: { $0 is CreativeCenterViewController || $0 is CustomTabBarController }) {
                nav.popToViewController(targetVC, animated: true)
                return
            }
            // Fallback: popToRoot
            nav.popToRootViewController(animated: true)
            return
        }
        // 如果没有 navigationController，尝试逐级 dismiss
        var presenter = self.presentingViewController
        while presenter != nil {
            if presenter is CustomTabBarController || presenter is CreativeCenterViewController {
                presenter?.dismiss(animated: true)
                return
            }
            presenter = presenter?.presentingViewController
        }
        // 最后备选：直接 dismiss 当前
        self.dismiss(animated: true)
    }

    // MARK: - 预填充逻辑

    /// 根据 `prefillInfo` 填充除"分类"外的所有字段。
    /// 分类须等接口返回后再行处理，见 `applyPrefillCategoryIfNeeded()`
    private func applyPrefillInfo() {
        guard let info = prefillInfo else { return }

        // 标题
        if let title = info.title, !title.isEmpty {
            titleTextView.text = title
            titleTextView.textColor = UIColor(hex: "#333333")
            characterCountLabel.text = "\(min(title.count, 100))/100"
        }

        // 权限
        if let privacy = info.privacy, (1...3).contains(privacy) {
            privacySelectedIndex = privacy - 1
            let options = ["所有人可见", "互相关注的人可见", "仅自己可见"]
            privacyStatusLabel.text = options[privacySelectedIndex]
            privacyStatusLabel.textColor = UIColor(hex: "#333333")
        }

        // 评论相关
        if let allow = info.allowComment { commentSwitch.setOn(allow, animated: false) }
        if let follow = info.followComment {
            followersCommentSwitch?.setOn(follow, animated: false)
            followersCommentSwitch?.isEnabled = commentSwitch.isOn
            followersCommentSwitch?.alpha = commentSwitch.isOn ? 1.0 : 0.5
        }

        // 定时发布
        if let schedule = info.scheduleTime, !schedule.isEmpty {
            timedPublishSwitch.setOn(true, animated: false)
            publishTimeLabel.text = schedule
            publishTimeLabel.isHidden = false
        }

        // 位置
        if let address = info.address, !address.isEmpty {
            locationSwitch?.setOn(true, animated: false)
            // UI 仅显示省+市，后台仍保留完整地址
            let displayAddress: String = {
                // 尝试截取到"市"字符
                if let cityRange = address.range(of: "市") {
                    return String(address[..<cityRange.upperBound])
                }
                // 尝试按照空格分割
                if let spaceIdx = address.firstIndex(of: " ") {
                    return String(address[..<spaceIdx])
                }
                // 否则返回原值
                return address
            }()
            locationStatusLabel?.text = displayAddress
            currentLocationString = displayAddress // 保持兼容
            currentFullAddress = address           // 供后台使用
        }
        if let lat = info.latitude, let lng = info.longitude {
            currentCoordinate = CLLocationCoordinate2D(latitude: lat, longitude: lng)
        }
        if let area = info.areaCode { currentCityId = area }
    }

    /// 在分类列表加载完成后调用，用于回显预填充的分类。
    private func applyPrefillCategoryIfNeeded() {
        guard let id = prefillCategoryId else {
            #if DEBUG
            print("[编辑模式] 无 prefillCategoryId，跳过分类回显")
            #endif
            return
        }
        #if DEBUG
        print("[编辑模式] 开始分类回显，目标 categoryId: \(id)")
        print("[编辑模式] 当前 categoryIds: \(categoryIds)")
        print("[编辑模式] 当前 categoryTitles: \(categoryTitles)")
        #endif
        if let idx = categoryIds.firstIndex(of: id) {
            #if DEBUG
            print("[编辑模式] 按 id 匹配成功，index=\(idx)")
            #endif
            selectedCategoryIndex = idx
            if let label = findCategoryTextLabel() {
                label.text = categoryTitles[idx]
                label.textColor = UIColor(hex: "#333333")
            }
            prefillCategoryId = nil
        } else if let name = prefillInfo?.categoryName, let idx2 = categoryTitles.firstIndex(of: name) {
            #if DEBUG
            print("[编辑模式] 按 name 匹配成功: \(name) index=\(idx2)")
            #endif
            selectedCategoryIndex = idx2
            if let label = findCategoryTextLabel() {
                label.text = categoryTitles[idx2]
                label.textColor = UIColor(hex: "#333333")
            }
            prefillCategoryId = nil
        } else {
            #if DEBUG
            print("[编辑模式] 分类回显失败，未匹配到 id 或 name")
            #endif
        }
    }

    /// 仅更新视频信息，不涉及上传
    private func updateVideoInfoOnly() {
        guard let worksId = prefillInfo?.worksId else { return }

        // 组装参数
        var params: [String: Any] = ["id": worksId]
        let title = titleTextView.text.trimmingCharacters(in: .whitespacesAndNewlines)
        params["worksTitle"] = (title == "请添加标题") ? "" : title

        // 分类
        if isDraftEditMode && draftsId > 0 {
            // 草稿编辑模式：将草稿ID填入worksCategoryId
            params["worksCategoryId"] = draftsId
        } else {
            // 正常发布模式：使用选择的分类ID
            let idx = selectedCategoryIndex ?? 0
            if categoryIds.indices.contains(idx) {
                params["worksCategoryId"] = categoryIds[idx]
            }
        }

        // 权限
        params["privacy"] = privacySelectedIndex + 1

        // 评论
        params["allowComment"] = commentSwitch.isOn ? 1 : 0
        params["followComment"] = followersCommentSwitch?.isOn == true ? 1 : 0

        // 经纬度、地址
        if let lat = currentCoordinate?.latitude { params["lat"] = String(lat) }
        if let lng = currentCoordinate?.longitude { params["lng"] = String(lng) }
        if let areaCode = currentCityId { params["areaCode"] = areaCode }
        if let address = currentFullAddress { params["address"] = address }

        // 定时发布
        if timedPublishSwitch.isOn, let text = publishTimeLabel.text, !text.isEmpty, text != "yyyy-mm-dd hh:mm" {
            params["extValue"] = text
        }

        // 添加编辑配置埋点信息
        if !usedEditConfigs.isEmpty {
            params["useEditConfigId"] = usedEditConfigs
        }

        // 显示加载状态
        publishButton.isEnabled = false
        publishButton.setTitle("提交中...", for: .normal)

        // 这里只示例复用 uploadShortVideoWorks 接口，实际应调用后台提供的"修改作品"接口
        APIManager.shared.uploadShortVideoWorks(params: params) { [weak self] result in
            DispatchQueue.main.async {
                guard let self = self else { return }
                self.publishButton.isEnabled = true
                self.publishButton.setTitle("保存", for: .normal)
                switch result {
                case .success(let response):
                    // 检查业务逻辑是否成功
                    if response.isSuccess {
                        let alert = UIAlertController(title: "保存成功", message: nil, preferredStyle: .alert)
                        alert.addAction(UIAlertAction(title: "确定", style: .default) { _ in
                            self.navigateBackToPublishEntrance()
                        })
                        self.present(alert, animated: true)
                    } else {
                        // 业务逻辑失败，显示服务器返回的错误信息
                        self.showAlert(title: "保存失败", message: response.displayMessage)
                    }
                case .failure(let error):
                    self.showAlert(title: "保存失败", message: error.errorMessage)
                }
            }
        }
    }
}

// 在类的最后添加扩展
extension VideoEditingDetailsViewController: UITextViewDelegate {
    // 处理文本变化
    func textViewDidChange(_ textView: UITextView) {
        if textView == titleTextView {
            // 更新字数显示
            let count = textView.text.count
            characterCountLabel.text = "\(count)/100"
            
            // 如果超过100字，截断文本
            if count > 100 {
                textView.text = String(textView.text.prefix(100))
                characterCountLabel.text = "100/100"
            }
        }
    }
    
    // 处理开始编辑时的占位文本
    func textViewDidBeginEditing(_ textView: UITextView) {
        if textView == titleTextView && textView.textColor == UIColor(hex: "#999999") {
            textView.text = ""
            textView.textColor = UIColor(hex: "#333333")
        }
    }
    
    // 处理结束编辑时的占位文本
    func textViewDidEndEditing(_ textView: UITextView) {
        if textView == titleTextView && textView.text.isEmpty {
            textView.text = "请添加标题"
            textView.textColor = UIColor(hex: "#999999")
            characterCountLabel.text = "0/100"
        }
    }
}

// MARK: - UIPickerViewDataSource & Delegate
extension VideoEditingDetailsViewController: UIPickerViewDataSource, UIPickerViewDelegate {
    func numberOfComponents(in pickerView: UIPickerView) -> Int { 1 }
    func pickerView(_ pickerView: UIPickerView, numberOfRowsInComponent component: Int) -> Int {
        return categoryTitles.count
    }
    func pickerView(_ pickerView: UIPickerView, titleForRow row: Int, forComponent component: Int) -> String? {
        return categoryTitles[row]
    }
}

extension VideoEditingDetailsViewController: TXVideoPublishListener {
    func onPublishProgress(_ uploadBytes: Int32, totalBytes: Int32) {
        // 不显示详细进度，保持简单的发布中状态
        // 进度信息仅用于调试日志
        let progress = totalBytes == 0 ? 0 : Float(uploadBytes) / Float(totalBytes)
        let percent = Int(progress * 100)
        print("[发布] 上传进度: \(percent)%")
    }

    func onPublishComplete(_ result: TXPublishResult!) {
        DispatchQueue.main.async {
            self.isPublishing = false
            self.publishProgressAlert?.dismiss(animated: true)
            // 打印所有发布结果参数
            print("[发布] TXPublishResult: retCode=\(result.retCode), descMsg=\(result.descMsg ?? ""), videoId=\(result.videoId ?? ""), videoURL=\(result.videoURL ?? ""), coverURL=\(result.coverURL ?? "")")
            if self.hasCustomCover {
                print("[发布] 使用自定义封面，腾讯云返回封面URL: \(result.coverURL ?? "")")
            } else {
                print("[发布] 未选择自定义封面，腾讯云自动取首帧封面URL: \(result.coverURL ?? "")")
            }
            // 恢复按钮
            self.publishButton.isEnabled = true
            self.publishButton.setTitle("发布", for: .normal)

            if result.retCode == 0 {
                // 1. 组装业务参数
                var params: [String: Any] = [:]
                // 标题
                let title = self.titleTextView.text.trimmingCharacters(in: .whitespacesAndNewlines)
                params["worksTitle"] = (title == "请添加标题") ? "" : title
                // 分类
                if self.isDraftEditMode && self.draftsId > 0 {
                    // 草稿编辑模式：将草稿ID填入worksCategoryId
                    params["worksCategoryId"] = self.draftsId
                } else {
                    // 正常发布模式：使用选择的分类ID
                    let idx = self.selectedCategoryIndex ?? 0
                    if self.categoryIds.indices.contains(idx) {
                        params["worksCategoryId"] = self.categoryIds[idx]
                    }
                }
                // 谁可以看（权限类型，1-公开 2-互相关注的人可见 3-仅自己）
                params["privacy"] = (self.privacySelectedIndex) + 1
                // 允许评论（默认不允许）
                params["allowComment"] = self.commentSwitch.isOn ? 1 : 0
                // 仅关注的人可评论（默认关闭）
                params["followComment"] = self.followersCommentSwitch?.isOn == true ? 1 : 0
                // 经纬度、地址、行政区划
                if let lat = self.currentCoordinate?.latitude { params["lat"] = String(lat) }
                if let lng = self.currentCoordinate?.longitude { params["lng"] = String(lng) }
                if let areaCode = self.currentCityId, !areaCode.isEmpty { params["areaCode"] = areaCode }
                if let address = self.currentFullAddress, !address.isEmpty { params["address"] = address }
                // 定时发布（默认不定时）
                if self.timedPublishSwitch.isOn, let text = self.publishTimeLabel.text, !text.isEmpty, text != "yyyy-mm-dd hh:mm" {
                    params["extValue"] = text
                }
                // 视频相关
                if self.isDraftEditMode && self.draftsId > 0 {
                    // 草稿编辑模式：使用草稿的原始数据
                    if let prefill = self.prefillInfo {
                        params["videoId"] = prefill.videoId ?? ""

                        // 解析worksUrl JSON字符串
                        if let worksUrlString = prefill.worksUrl, !worksUrlString.isEmpty {
                            if let data = worksUrlString.data(using: .utf8),
                               let urlArray = try? JSONSerialization.jsonObject(with: data, options: []) as? [String] {
                                params["worksUrl"] = urlArray
                            } else {
                                params["worksUrl"] = [worksUrlString] // 如果解析失败，直接作为单个URL
                            }
                        } else {
                            params["worksUrl"] = []
                        }

                        // 使用草稿的封面，如果用户更换了封面则使用新的
                        if self.hasCustomCover {
                            params["worksCoverImg"] = result.coverURL ?? ""
                        } else {
                            params["worksCoverImg"] = prefill.worksCoverImg ?? ""
                        }
                    }
                } else {
                    // 正常发布模式：使用腾讯云返回的数据
                    params["videoId"] = result.videoId ?? ""
                    params["worksUrl"] = [result.videoURL ?? ""]
                    params["worksCoverImg"] = result.coverURL ?? ""
                }

                params["duration"] = Int(self.videoDuration ?? 0)
                params["size"] = Int(self.videoSize ?? 0)
                params["worksType"] = 1 // 1-视频

                // 添加编辑配置埋点信息
                if !self.usedEditConfigs.isEmpty {
                    params["useEditConfigId"] = self.usedEditConfigs
                }

                // 如果是草稿编辑模式，添加draftsId参数
                if self.isDraftEditMode && self.draftsId > 0 {
                    params["draftsId"] = self.draftsId
                }

                // 确保所有必需参数都有默认值
                if params["address"] == nil { params["address"] = "" }
                if params["areaCode"] == nil { params["areaCode"] = "" }
                if params["extValue"] == nil { params["extValue"] = "" }
                if params["lat"] == nil { params["lat"] = "" }
                if params["lng"] == nil { params["lng"] = "" }
                if params["goodIds"] == nil { params["goodIds"] = [] }
                if params["useEditConfigId"] == nil { params["useEditConfigId"] = [:] }

                // 2. 打印最终入参
                print(self.isSavingDraft ? "[草稿] 最终入参: \(params)" : "[发布] 最终入参: \(params)")

                // 3. 调用对应接口
                if self.isSavingDraft {
                    self.publishButton.isEnabled = false
                    self.publishButton.setTitle("正在保存...", for: .normal)
                    APIManager.shared.saveVideoWorksDrafts(params: params) { [weak self] apiResult in
                        DispatchQueue.main.async {
                            guard let self = self else { return }
                            self.publishButton.isEnabled = true
                            self.publishButton.setTitle("发布", for: .normal)
                            self.isSavingDraft = false
                            switch apiResult {
                            case .success(let response):
                                // 检查业务逻辑是否成功
                                if response.isSuccess {
                                    let alert = UIAlertController(title: "保存成功", message: "草稿已保存！", preferredStyle: .alert)
                                    alert.addAction(UIAlertAction(title: "确定", style: .default) { _ in
                                        self.navigateBackToPublishEntrance()
                                    })
                                    self.present(alert, animated: true)
                                } else {
                                    // 业务逻辑失败，显示服务器返回的错误信息
                                    let alert = UIAlertController(title: "保存失败", message: response.displayMessage, preferredStyle: .alert)
                                    alert.addAction(UIAlertAction(title: "确定", style: .default))
                                    self.present(alert, animated: true)
                                }
                            case .failure(let error):
                                let alert = UIAlertController(title: "保存失败", message: error.errorMessage, preferredStyle: .alert)
                                alert.addAction(UIAlertAction(title: "确定", style: .default))
                                self.present(alert, animated: true)
                            }
                        }
                    }
                } else {
                    self.publishButton.isEnabled = false
                    self.publishButton.setTitle("正在提交...", for: .normal)
                    APIManager.shared.uploadShortVideoWorks(params: params) { [weak self] apiResult in
                        DispatchQueue.main.async {
                            guard let self = self else { return }
                            self.publishButton.isEnabled = true
                            self.publishButton.setTitle("发布", for: .normal)
                            switch apiResult {
                            case .success(let response):
                                // 检查业务逻辑是否成功
                                if response.isSuccess {
                                    // 如果是草稿编辑模式，发布成功后删除草稿
                                    if self.isDraftEditMode && self.draftsId > 0 {
                                        self.deleteDraftAfterPublish()
                                    } else {
                                        let alert = UIAlertController(title: "发布成功", message: "视频发布成功！", preferredStyle: .alert)
                                        alert.addAction(UIAlertAction(title: "确定", style: .default) { _ in
                                            self.navigateBackToPublishEntrance()
                                        })
                                        self.present(alert, animated: true)
                                    }
                                } else {
                                    // 业务逻辑失败，显示服务器返回的错误信息
                                    let alert = UIAlertController(title: "发布失败", message: response.displayMessage, preferredStyle: .alert)
                                    alert.addAction(UIAlertAction(title: "确定", style: .default))
                                    self.present(alert, animated: true)
                                }
                            case .failure(let error):
                                let alert = UIAlertController(title: "发布失败", message: error.errorMessage, preferredStyle: .alert)
                                alert.addAction(UIAlertAction(title: "确定", style: .default))
                                self.present(alert, animated: true)
                            }
                        }
                    }
                }

            } else {
                let msg = result.descMsg ?? "未知错误"
                print("[发布] 发布失败: \(msg)")
                let alert = UIAlertController(title: "上传失败", message: msg, preferredStyle: .alert)
                alert.addAction(UIAlertAction(title: "确定", style: .default))
                self.present(alert, animated: true)
            }
        }
    }
}

